a:9:{s:6:"_token";s:40:"TbnvYmxyEWNk38mgKzj2wLM1pDY9bFTHfORlGEAK";s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:3:"url";a:0:{}s:9:"_previous";a:1:{s:3:"url";s:33:"https://sucos.test/admin/expenses";}s:13:"livewire-urls";a:6:{s:8:"previous";s:57:"https://sucos.test/filament/exports/3/download?format=csv";s:14:"previous-route";s:25:"filament.exports.download";s:7:"current";s:58:"https://sucos.test/filament/exports/4/download?format=xlsx";s:13:"current-route";s:25:"filament.exports.download";s:7:"history";a:4:{i:0;s:29:"https://sucos.test/auth/login";i:1;s:58:"https://sucos.test/filament/exports/2/download?format=xlsx";i:2;s:57:"https://sucos.test/filament/exports/3/download?format=csv";i:3;s:58:"https://sucos.test/filament/exports/4/download?format=xlsx";}s:13:"history-route";a:4:{i:0;s:10:"auth.login";i:1;s:25:"filament.exports.download";i:2;s:25:"filament.exports.download";i:3;s:25:"filament.exports.download";}}s:50:"login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82";i:2;s:17:"password_hash_web";s:60:"$2y$10$LYD2DCaLq4wtjMcDp0Kug.WhQeNxMIEeGoc22/aZ.9sde2I37FHda";s:8:"filament";a:0:{}s:6:"tables";a:1:{s:34:"ListExpenseReports_toggled_columns";a:1:{s:4:"user";a:1:{s:4:"name";b:1;}}}}