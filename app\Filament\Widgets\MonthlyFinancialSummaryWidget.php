<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use App\Models\Expense;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class MonthlyFinancialSummaryWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Vendas deste mês
        $currentMonthSales = Sale::whereBetween('sale_date', [
            $now->copy()->startOfMonth(),
            $now->copy()->endOfMonth()
        ])->sum('total');

        // Despesas deste mês
        $currentMonthExpenses = Expense::whereBetween('expense_date', [
            $now->copy()->startOfMonth(),
            $now->copy()->endOfMonth()
        ])->sum('amount');

        // Cobranças liberadas (vendas do mês anterior, cobrança desde dia 01 deste mês)
        $previousMonth = $now->copy()->subMonth();
        $collectionsReleased = Sale::whereMonth('sale_date', $previousMonth->month)
            ->whereYear('sale_date', $previousMonth->year)
            ->sum('total');

        // Cobranças já recebidas (vendas do mês anterior que já foram pagas)
        $collectionsReceived = Sale::whereMonth('sale_date', $previousMonth->month)
            ->whereYear('sale_date', $previousMonth->year)
            ->where('paid', true)
            ->sum('total');

        // Lucro realizado (cobranças recebidas - despesas)
        $realizedProfit = $collectionsReceived - $currentMonthExpenses;

        // Verificar se já passou do dia 01 para mostrar status das cobranças
        $collectionDay = $now->copy()->day(1);
        $isCollectionPeriod = $now->gte($collectionDay);

        return [
            Stat::make('💰 Vendas Deste Mês', 'R$ ' . number_format($currentMonthSales, 2, ',', '.'))
                ->description('Vendas de ' . $now->format('F/Y'))
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('info')
                ->chart([15, 20, 25, 30, 35, 40, 45]),

            Stat::make('💸 Despesas Deste Mês', 'R$ ' . number_format($currentMonthExpenses, 2, ',', '.'))
                ->description('Gastos de ' . $now->format('F/Y'))
                ->descriptionIcon('heroicon-m-credit-card')
                ->color('warning')
                ->chart([10, 15, 20, 25, 30, 35, 40]),

            Stat::make('📋 Cobranças Liberadas', 'R$ ' . number_format($collectionsReleased, 2, ',', '.'))
                ->description($isCollectionPeriod ?
                    'Vendas de ' . $previousMonth->format('F/Y') . ' (desde 01/' . $now->format('m') . ')' :
                    'Libera em 01/' . $now->format('m') . ' (vendas de ' . $previousMonth->format('F') . ')')
                ->descriptionIcon($isCollectionPeriod ? 'heroicon-m-check-circle' : 'heroicon-m-clock')
                ->color($isCollectionPeriod ? 'success' : 'gray')
                ->chart([20, 25, 30, 35, 40, 45, 50]),

            Stat::make($realizedProfit >= 0 ? '📈 Lucro Realizado' : '📉 Prejuízo',
                'R$ ' . number_format(abs($realizedProfit), 2, ',', '.'))
                ->description('Cobranças recebidas - Despesas')
                ->descriptionIcon($realizedProfit >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($realizedProfit >= 0 ? 'success' : 'danger')
                ->chart($realizedProfit >= 0 ? [10, 15, 20, 25, 30, 35, 40] : [40, 35, 30, 25, 20, 15, 10]),
        ];
    }
}
