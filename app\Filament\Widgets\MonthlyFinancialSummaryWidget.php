<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use App\Models\Expense;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class MonthlyFinancialSummaryWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Vendas deste mês
        $currentMonthSales = Sale::whereBetween('sale_date', [
            $now->copy()->startOfMonth(),
            $now->copy()->endOfMonth()
        ])->sum('total');

        // Despesas deste mês
        $currentMonthExpenses = Expense::whereBetween('expense_date', [
            $now->copy()->startOfMonth(),
            $now->copy()->endOfMonth()
        ])->sum('amount');

        // Cobranças pendentes (vendas do mês anterior ainda não pagas)
        $previousMonth = $now->copy()->subMonth();
        $collectionsPending = Sale::whereMonth('sale_date', $previousMonth->month)
            ->whereYear('sale_date', $previousMonth->year)
            ->where('paid', false)
            ->sum('total');

        // Cobranças já recebidas (vendas do mês anterior que já foram pagas)
        $collectionsReceived = Sale::whereMonth('sale_date', $previousMonth->month)
            ->whereYear('sale_date', $previousMonth->year)
            ->where('paid', true)
            ->sum('total');

        // Lucro realizado (cobranças recebidas - despesas)
        $realizedProfit = $collectionsReceived - $currentMonthExpenses;

        return [
            Stat::make('💰 Vendas Deste Mês', 'R$ ' . number_format($currentMonthSales, 2, ',', '.'))
                ->description('Vendas de ' . $now->format('F/Y'))
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('info')
                ->chart([15, 20, 25, 30, 35, 40, 45]),

            Stat::make('💸 Despesas Deste Mês', 'R$ ' . number_format($currentMonthExpenses, 2, ',', '.'))
                ->description('Gastos de ' . $now->format('F/Y'))
                ->descriptionIcon('heroicon-m-credit-card')
                ->color('warning')
                ->chart([10, 15, 20, 25, 30, 35, 40]),

            Stat::make('📋 Cobranças Pendentes', 'R$ ' . number_format($collectionsPending, 2, ',', '.'))
                ->description($collectionsPending > 0 ?
                    'Vendas de ' . $previousMonth->format('F/Y') . ' ainda não pagas' :
                    'Todas as vendas de ' . $previousMonth->format('F/Y') . ' foram pagas!')
                ->descriptionIcon($collectionsPending > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-check-circle')
                ->color($collectionsPending > 0 ? 'warning' : 'success')
                ->chart($collectionsPending > 0 ? [20, 25, 30, 35, 40, 45, 50] : [50, 45, 40, 35, 30, 25, 20]),

            Stat::make($realizedProfit >= 0 ? '📈 Lucro Realizado' : '📉 Prejuízo',
                'R$ ' . number_format(abs($realizedProfit), 2, ',', '.'))
                ->description('Cobranças recebidas - Despesas')
                ->descriptionIcon($realizedProfit >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($realizedProfit >= 0 ? 'success' : 'danger')
                ->chart($realizedProfit >= 0 ? [10, 15, 20, 25, 30, 35, 40] : [40, 35, 30, 25, 20, 15, 10]),
        ];
    }
}
