<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExpenseResource\Pages;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Leandrocfe\FilamentPtbrFormFields\Money;

class ExpenseResource extends Resource
{
    protected static ?string $label = 'Despesa';

    protected static ?string $pluralModelLabel = 'Despesas';

    protected static ?string $model = Expense::class;

    protected static ?string $recordTitleAttribute = 'description';

    protected static ?string $navigationIcon = 'phosphor-money';

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationGroup = 'Financeiro';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('description')
                    ->label('Descrição')
                    ->required()
                    ->maxLength(191)
                    ->columnSpanFull(),

                Money::make('amount')
                    ->label('Valor')
                    ->required()
                    ->rules(['min:0.01']),

                Forms\Components\DatePicker::make('expense_date')
                    ->label('Data da Despesa')
                    ->required()
                    ->default(now())
                    ->maxDate(now()),

                Forms\Components\Select::make('expense_category_id')
                    ->label('Categoria')
                    ->options(ExpenseCategory::active()->pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->preload()
                    ->relationship(name: 'expenseCategory', titleAttribute: 'name')
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->label('Nome')
                            ->required()
                            ->maxLength(191),
                        Forms\Components\Textarea::make('description')
                            ->label('Descrição')
                            ->rows(3),
                        Forms\Components\ColorPicker::make('color')
                            ->label('Cor')
                            ->hex(),
                        Forms\Components\Toggle::make('active')
                            ->label('Ativo')
                            ->default(true),
                    ]),

                Forms\Components\Select::make('payment_method')
                    ->label('Método de Pagamento')
                    ->options([
                        'dinheiro' => 'Dinheiro',
                        'pix' => 'PIX',
                        'cartao_debito' => 'Cartão de Débito',
                        'cartao_credito' => 'Cartão de Crédito',
                        'transferencia' => 'Transferência',
                        'boleto' => 'Boleto',
                    ])
                    ->required()
                    ->native(false),

                Forms\Components\Toggle::make('paid')
                    ->label('Pago')
                    ->default(false)
                    ->live(),

                Forms\Components\DatePicker::make('payment_date')
                    ->label('Data do Pagamento')
                    ->visible(fn (Forms\Get $get): bool => $get('paid'))
                    ->required(fn (Forms\Get $get): bool => $get('paid'))
                    ->maxDate(now()),

                Forms\Components\Textarea::make('notes')
                    ->label('Observações')
                    ->rows(3)
                    ->columnSpanFull(),

                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('description')
                    ->label('Descrição')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Valor')
                    ->money('BRL')
                    ->sortable(),

                Tables\Columns\TextColumn::make('expense_date')
                    ->label('Data')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('expenseCategory.name')
                    ->label('Categoria')
                    ->badge()
                    ->color(fn (Expense $record): string => $record->expenseCategory->color ?? 'gray')
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method_label')
                    ->label('Método')
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('paid')
                    ->label('Pago')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Data Pagamento')
                    ->date('d/m/Y')
                    ->placeholder('Não pago')
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Usuário')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('expense_category_id')
                    ->label('Categoria')
                    ->relationship('expenseCategory', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Método de Pagamento')
                    ->options([
                        'dinheiro' => 'Dinheiro',
                        'pix' => 'PIX',
                        'cartao_debito' => 'Cartão de Débito',
                        'cartao_credito' => 'Cartão de Crédito',
                        'transferencia' => 'Transferência',
                        'boleto' => 'Boleto',
                    ])
                    ->native(false),

                Tables\Filters\TernaryFilter::make('paid')
                    ->label('Status de Pagamento')
                    ->boolean()
                    ->trueLabel('Apenas pagas')
                    ->falseLabel('Apenas não pagas')
                    ->native(false),

                Tables\Filters\Filter::make('expense_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('De'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Até'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('expense_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('expense_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('toggle_paid')
                    ->label(fn (Expense $record): string => $record->paid ? 'Marcar como não pago' : 'Marcar como pago')
                    ->icon(fn (Expense $record): string => $record->paid ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (Expense $record): string => $record->paid ? 'danger' : 'success')
                    ->action(function (Expense $record): void {
                        $record->update([
                            'paid' => !$record->paid,
                            'payment_date' => !$record->paid ? now() : null,
                        ]);
                    })
                    ->requiresConfirmation(),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('expense_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenses::route('/'),
            'create' => Pages\CreateExpense::route('/create'),
            'edit' => Pages\EditExpense::route('/{record}/edit'),
        ];
    }
}
