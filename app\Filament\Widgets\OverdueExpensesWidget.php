<?php

namespace App\Filament\Widgets;

use App\Models\Expense;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class OverdueExpensesWidget extends BaseWidget
{
    protected static ?int $sort = 53;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Despesas em atraso (não pagas há mais de 30 dias)
        $overdueExpenses = Expense::where('paid', false)
            ->where('expense_date', '<=', $now->copy()->subDays(30))
            ->get();

        $overdueCount = $overdueExpenses->count();
        $overdueAmount = $overdueExpenses->sum('amount');

        // Despesas vencendo em 7 dias (não pagas há mais de 23 dias)
        $soonOverdueExpenses = Expense::where('paid', false)
            ->whereBetween('expense_date', [
                $now->copy()->subDays(30),
                $now->copy()->subDays(23)
            ])
            ->get();

        $soonOverdueCount = $soonOverdueExpenses->count();
        $soonOverdueAmount = $soonOverdueExpenses->sum('amount');

        // Despesas não pagas este mês
        $currentMonthUnpaid = Expense::where('paid', false)
            ->whereBetween('expense_date', [
                $now->copy()->startOfMonth(),
                $now->copy()->endOfMonth()
            ])
            ->get();

        $currentMonthUnpaidCount = $currentMonthUnpaid->count();
        $currentMonthUnpaidAmount = $currentMonthUnpaid->sum('amount');

        return [
            Stat::make('🚨 Despesas em Atraso', $overdueCount . ' despesas')
                ->description('R$ ' . number_format($overdueAmount, 2, ',', '.') . ' em atraso há +30 dias')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($overdueCount > 0 ? 'danger' : 'success')
                ->chart($overdueCount > 0 ? [10, 15, 20, 25, 30, 35, 40] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('⚠️ Vencendo em Breve', $soonOverdueCount . ' despesas')
                ->description('R$ ' . number_format($soonOverdueAmount, 2, ',', '.') . ' vencendo em 7 dias')
                ->descriptionIcon('heroicon-m-clock')
                ->color($soonOverdueCount > 0 ? 'warning' : 'success')
                ->chart($soonOverdueCount > 0 ? [5, 8, 12, 15, 18, 22, 25] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('📋 Pendentes Este Mês', $currentMonthUnpaidCount . ' despesas')
                ->description('R$ ' . number_format($currentMonthUnpaidAmount, 2, ',', '.') . ' não pagas')
                ->descriptionIcon('heroicon-m-document-text')
                ->color($currentMonthUnpaidCount > 0 ? 'info' : 'success')
                ->chart([3, 6, 9, 12, 15, 18, 21]),
        ];
    }
}
