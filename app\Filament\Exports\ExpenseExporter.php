<?php

namespace App\Filament\Exports;

use App\Models\Expense;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class ExpenseExporter extends Exporter
{
    protected static ?string $model = Expense::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('expense_date')
                ->label('Data da Despesa'),

            ExportColumn::make('description')
                ->label('Descrição'),

            ExportColumn::make('expenseCategory.name')
                ->label('Categoria'),

            ExportColumn::make('amount')
                ->label('Valor')
                ->formatStateUsing(fn ($state) => 'R$ ' . number_format($state, 2, ',', '.')),

            ExportColumn::make('payment_method_label')
                ->label('Método de Pagamento'),

            ExportColumn::make('paid')
                ->label('Status')
                ->formatStateUsing(fn ($state) => $state ? 'Pago' : 'Pendente'),

            ExportColumn::make('payment_date')
                ->label('Data do Pagamento'),

            ExportColumn::make('notes')
                ->label('Observações'),

            ExportColumn::make('user.name')
                ->label('Usuário'),

            ExportColumn::make('created_at')
                ->label('Criado em'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Sua exportação de despesas foi concluída e ' . number_format($export->successful_rows) . ' ' . str('linha')->plural($export->successful_rows) . ' foram exportadas.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('linha')->plural($failedRowsCount) . ' falharam na exportação.';
        }

        return $body;
    }
}
