<?php

namespace App\Filament\Widgets;

use App\Models\Expense;
use App\Models\ExpenseCategory;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class ExpenseStatsWidget extends BaseWidget
{
    protected static ?int $sort = 50;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Despesas deste mês
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        $currentMonthExpenses = Expense::whereBetween('expense_date', [$currentMonth, $endOfMonth])->get();
        $totalCurrentMonth = $currentMonthExpenses->sum('amount');
        $paidCurrentMonth = $currentMonthExpenses->where('paid', true)->sum('amount');
        $unpaidCurrentMonth = $currentMonthExpenses->where('paid', false)->sum('amount');

        // Despesas do mês passado para comparação
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $previousMonthTotal = Expense::whereBetween('expense_date', [$previousMonth, $endOfPreviousMonth])->sum('amount');

        // Calcular variação percentual
        $variation = 0;
        if ($previousMonthTotal > 0) {
            $variation = (($totalCurrentMonth - $previousMonthTotal) / $previousMonthTotal) * 100;
        }

        // Categoria com mais gastos este mês
        $topCategory = $currentMonthExpenses->groupBy('expense_category_id')
            ->map(function ($expenses) {
                return [
                    'category' => $expenses->first()->expenseCategory->name ?? 'N/A',
                    'total' => $expenses->sum('amount')
                ];
            })
            ->sortByDesc('total')
            ->first();

        return [
            Stat::make('💸 Despesas Este Mês', 'R$ ' . number_format($totalCurrentMonth, 2, ',', '.'))
                ->description($variation >= 0 ?
                    '+' . number_format(abs($variation), 1) . '% vs mês anterior' :
                    '-' . number_format(abs($variation), 1) . '% vs mês anterior')
                ->descriptionIcon($variation >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($variation <= 0 ? 'success' : ($variation <= 20 ? 'warning' : 'danger'))
                ->chart([15, 18, 22, 25, 28, 32, 35]),

            Stat::make('✅ Despesas Pagas', 'R$ ' . number_format($paidCurrentMonth, 2, ',', '.'))
                ->description('De R$ ' . number_format($totalCurrentMonth, 2, ',', '.') . ' total')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success')
                ->chart([8, 12, 16, 20, 24, 28, 32]),

            Stat::make('⏳ Despesas Pendentes', 'R$ ' . number_format($unpaidCurrentMonth, 2, ',', '.'))
                ->description('Categoria top: ' . ($topCategory['category'] ?? 'N/A'))
                ->descriptionIcon('heroicon-m-clock')
                ->color($unpaidCurrentMonth > 0 ? 'warning' : 'success')
                ->chart([5, 8, 12, 15, 18, 22, 25]),
        ];
    }
}
