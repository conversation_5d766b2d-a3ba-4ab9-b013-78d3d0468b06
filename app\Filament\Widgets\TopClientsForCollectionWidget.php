<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use App\Models\Person;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class TopClientsForCollectionWidget extends BaseWidget
{
    protected static ?string $heading = '👥 Top Clientes para Cobrança';

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        $now = Carbon::now();
        $previousMonth = $now->copy()->subMonth();
        $collectionReleaseDate = $now->copy()->day(1);
        $defaultDate = $now->copy()->day(16);

        return $table
            ->query(
                Person::query()
                    ->whereHas('sales', function (Builder $query) use ($previousMonth) {
                        $query->whereMonth('sale_date', $previousMonth->month)
                            ->whereYear('sale_date', $previousMonth->year)
                            ->where('paid', false);
                    })
                    ->withSum(['sales as pending_amount' => function (Builder $query) use ($previousMonth) {
                        $query->whereMonth('sale_date', $previousMonth->month)
                            ->whereYear('sale_date', $previousMonth->year)
                            ->where('paid', false);
                    }], 'total')
                    ->withCount(['sales as pending_count' => function (Builder $query) use ($previousMonth) {
                        $query->whereMonth('sale_date', $previousMonth->month)
                            ->whereYear('sale_date', $previousMonth->year)
                            ->where('paid', false);
                    }])
                    ->orderBy('pending_amount', 'desc')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Cliente')
                    ->searchable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('department.name')
                    ->label('Departamento')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('phone')
                    ->label('Telefone')
                    ->formatStateUsing(fn (string $state): string =>
                        '(' . substr($state, 0, 2) . ') ' . substr($state, 2, 5) . '-' . substr($state, 7)
                    )
                    ->copyable()
                    ->copyMessage('Telefone copiado!')
                    ->icon('heroicon-m-phone'),

                Tables\Columns\TextColumn::make('pending_count')
                    ->label('Vendas')
                    ->alignCenter()
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('pending_amount')
                    ->label('Valor Pendente')
                    ->money('BRL')
                    ->weight('bold')
                    ->color(fn ($state) => $state > 500 ? 'danger' : ($state > 200 ? 'warning' : 'success')),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->getStateUsing(function () use ($now, $collectionReleaseDate, $defaultDate) {
                        if ($now->lt($collectionReleaseDate)) {
                            return 'Aguardando';
                        } elseif ($now->lt($defaultDate)) {
                            return 'Para Cobrar';
                        } else {
                            return 'Inadimplente';
                        }
                    })
                    ->color(function () use ($now, $collectionReleaseDate, $defaultDate) {
                        if ($now->lt($collectionReleaseDate)) {
                            return 'gray';
                        } elseif ($now->lt($defaultDate)) {
                            return 'warning';
                        } else {
                            return 'danger';
                        }
                    }),

                Tables\Columns\TextColumn::make('days_status')
                    ->label('Situação')
                    ->getStateUsing(function () use ($now, $collectionReleaseDate, $defaultDate) {
                        if ($now->lt($collectionReleaseDate)) {
                            $daysUntilCollection = $now->diffInDays($collectionReleaseDate);
                            return "Libera em {$daysUntilCollection} dias";
                        } elseif ($now->lt($defaultDate)) {
                            $daysUntilDefault = $now->diffInDays($defaultDate);
                            return "Vence em {$daysUntilDefault} dias";
                        } else {
                            $daysOverdue = $defaultDate->diffInDays($now);
                            return "{$daysOverdue} dias em atraso";
                        }
                    })
                    ->color(function () use ($now, $collectionReleaseDate, $defaultDate) {
                        if ($now->lt($collectionReleaseDate)) {
                            return 'gray';
                        } elseif ($now->lt($defaultDate)) {
                            return 'warning';
                        } else {
                            return 'danger';
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('whatsapp')
                    ->label('WhatsApp')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('success')
                    ->url(fn (Person $record): string =>
                        'https://wa.me/' . $record->phone_with_ddi . '?text=' .
                        urlencode("Olá {$record->name}! Temos vendas pendentes de pagamento. Podemos conversar?")
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('mark_paid')
                    ->label('Marcar como Pago')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (Person $record) use ($previousMonth) {
                        Sale::where('person_id', $record->id)
                            ->whereMonth('sale_date', $previousMonth->month)
                            ->whereYear('sale_date', $previousMonth->year)
                            ->where('paid', false)
                            ->update([
                                'paid' => true,
                                'payment_date' => now()
                            ]);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Confirmar Pagamento')
                    ->modalDescription('Marcar todas as vendas pendentes deste cliente como pagas?'),
            ])
            ->emptyStateHeading('🎉 Nenhuma cobrança pendente!')
            ->emptyStateDescription('Todos os clientes estão em dia com os pagamentos.')
            ->emptyStateIcon('heroicon-o-check-badge');
    }
}
