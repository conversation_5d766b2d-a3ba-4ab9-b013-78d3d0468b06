<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'value' => ''
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'value' => ''
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div data-auth="email-read-only-placeholder" <?php echo e($attributes->merge(['class' => 'px-3.5 bg-gray-50 py-2.5 text-sm flex items-center justify-between border rounded-md border-gray-300'])); ?>>
    <span><?php echo e($value); ?></span>
    <?php echo e($slot); ?>

</div><?php /**PATH E:\projects\My\sucos\vendor\devdojo\auth\src/../resources/views/components/elements/input-placeholder.blade.php ENDPATH**/ ?>