<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class CollectionStatusWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Data de liberação das cobranças (dia 05 deste mês)
        $collectionReleaseDate = $now->copy()->day(5);

        // Data limite para inadimplência (dia 20 deste mês)
        $defaultDate = $now->copy()->day(20);

        // Vendas do mês anterior (que devem ser cobradas este mês)
        $previousMonth = $now->copy()->subMonth();
        $salesForCollection = Sale::whereBetween('sale_date', [
            $previousMonth->startOfMonth(),
            $previousMonth->endOfMonth()
        ]);

        // Cobranças liberadas (desde dia 05)
        $releasedForCollection = $salesForCollection->clone();
        $releasedCount = $releasedForCollection->count();
        $releasedAmount = $releasedForCollection->sum('total');

        // Inadimplentes (após dia 20 e ainda não pagas)
        $defaultSales = $now->gt($defaultDate) ?
            $salesForCollection->clone()->where('paid', false) :
            collect([]);
        $defaultCount = $defaultSales->count();
        $defaultAmount = $defaultSales->sum('total');

        // Vendas pagas (do mês anterior)
        $paidSales = $salesForCollection->clone()->where('paid', true);
        $paidCount = $paidSales->count();
        $paidAmount = $paidSales->sum('total');

        // Taxa de inadimplência
        $defaultRate = $releasedCount > 0 ? ($defaultCount / $releasedCount) * 100 : 0;

        // Status das cobranças
        $collectionStatus = '';
        $statusColor = 'gray';

        if ($now->lt($collectionReleaseDate)) {
            $collectionStatus = 'Libera em ' . $collectionReleaseDate->format('d/m');
            $statusColor = 'gray';
        } elseif ($now->lt($defaultDate)) {
            $collectionStatus = 'Período de cobrança (até ' . $defaultDate->format('d/m') . ')';
            $statusColor = 'warning';
        } else {
            $collectionStatus = 'Inadimplência (após ' . $defaultDate->format('d/m') . ')';
            $statusColor = 'danger';
        }

        return [
            Stat::make('📋 Liberadas para Cobrança', $releasedCount . ' vendas')
                ->description('R$ ' . number_format($releasedAmount, 2, ',', '.') . ' • ' . $collectionStatus)
                ->descriptionIcon($now->gte($collectionReleaseDate) ? 'heroicon-m-check-circle' : 'heroicon-m-clock')
                ->color($statusColor)
                ->chart([10, 15, 20, 25, 30, 35, 40]),

            Stat::make('⚠️ Inadimplentes', $defaultCount . ' clientes')
                ->description('R$ ' . number_format($defaultAmount, 2, ',', '.') . ' em atraso')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($defaultCount > 0 ? 'danger' : 'success')
                ->chart($defaultCount > 0 ? [5, 10, 15, 20, 25, 30, 35] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('✅ Cobranças Recebidas', $paidCount . ' pagas')
                ->description('R$ ' . number_format($paidAmount, 2, ',', '.') . ' • Taxa: ' . number_format(100 - $defaultRate, 1) . '%')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('success')
                ->chart([20, 25, 30, 35, 40, 45, 50]),
        ];
    }
}
