<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class SalesVsCollectionsChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Vendas vs Cobranças (Últimos 12 Meses)';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $salesData = [];
        $collectionsData = [];
        $labels = [];

        // Últimos 12 meses
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);

            // Vendas do mês
            $salesAmount = Sale::whereMonth('sale_date', $month->month)
                ->whereYear('sale_date', $month->year)
                ->sum('total');

            // Cobranças do mês (vendas do mês anterior que foram pagas neste mês)
            $previousMonth = $month->copy()->subMonth();
            $collectionsAmount = Sale::whereMonth('sale_date', $previousMonth->month)
                ->whereYear('sale_date', $previousMonth->year)
                ->where('paid', true)
                ->whereBetween('payment_date', [
                    $month->startOfMonth(),
                    $month->endOfMonth()
                ])
                ->sum('total');

            $salesData[] = (float) $salesAmount;
            $collectionsData[] = (float) $collectionsAmount;
            $labels[] = $this->getMonthLabel($month);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Vendas Realizadas',
                    'data' => $salesData,
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => false,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Cobranças Recebidas',
                    'data' => $collectionsData,
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => false,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.dataset.label + ": R$ " + context.parsed.y.toLocaleString("pt-BR", {minimumFractionDigits: 2});
                        }'
                    ]
                ]
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) {
                            return "R$ " + value.toLocaleString("pt-BR", {minimumFractionDigits: 2});
                        }'
                    ]
                ]
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }

    private function getMonthLabel(Carbon $date): string
    {
        $months = [
            1 => 'Jan', 2 => 'Fev', 3 => 'Mar', 4 => 'Abr',
            5 => 'Mai', 6 => 'Jun', 7 => 'Jul', 8 => 'Ago',
            9 => 'Set', 10 => 'Out', 11 => 'Nov', 12 => 'Dez'
        ];

        return $months[$date->month] . '/' . $date->format('y');
    }
}
