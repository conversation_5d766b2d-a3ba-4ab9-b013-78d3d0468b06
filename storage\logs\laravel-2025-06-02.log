[2025-06-02 00:00:44] local.INFO: Installed plugins: []  
[2025-06-02 00:01:44] local.INFO: Installed plugins: []  
[2025-06-02 00:01:44] local.INFO: Installed plugins: []  
[2025-06-02 00:02:44] local.INFO: Installed plugins: []  
[2025-06-02 00:02:44] local.INFO: Installed plugins: []  
[2025-06-02 00:03:44] local.INFO: Installed plugins: []  
[2025-06-02 00:03:44] local.INFO: Installed plugins: []  
[2025-06-02 00:04:44] local.INFO: Installed plugins: []  
[2025-06-02 00:05:40] local.INFO: Installed plugins: []  
[2025-06-02 00:05:44] local.INFO: Installed plugins: []  
[2025-06-02 00:05:44] local.INFO: Installed plugins: []  
[2025-06-02 00:06:44] local.INFO: Installed plugins: []  
[2025-06-02 00:06:44] local.INFO: Installed plugins: []  
[2025-06-02 00:07:44] local.INFO: Installed plugins: []  
[2025-06-02 00:08:44] local.INFO: Installed plugins: []  
[2025-06-02 00:08:44] local.INFO: Installed plugins: []  
[2025-06-02 00:09:44] local.INFO: Installed plugins: []  
[2025-06-02 00:09:44] local.INFO: Installed plugins: []  
[2025-06-02 00:10:44] local.INFO: Installed plugins: []  
[2025-06-02 00:11:44] local.INFO: Installed plugins: []  
[2025-06-02 00:11:44] local.INFO: Installed plugins: []  
[2025-06-02 00:12:44] local.INFO: Installed plugins: []  
[2025-06-02 00:12:44] local.INFO: Installed plugins: []  
[2025-06-02 00:13:44] local.INFO: Installed plugins: []  
[2025-06-02 00:14:44] local.INFO: Installed plugins: []  
[2025-06-02 00:15:40] local.INFO: Installed plugins: []  
[2025-06-02 00:15:44] local.INFO: Installed plugins: []  
[2025-06-02 00:16:44] local.INFO: Installed plugins: []  
[2025-06-02 00:17:44] local.INFO: Installed plugins: []  
[2025-06-02 00:17:44] local.INFO: Installed plugins: []  
[2025-06-02 00:18:44] local.INFO: Installed plugins: []  
[2025-06-02 00:18:44] local.INFO: Installed plugins: []  
[2025-06-02 00:19:44] local.INFO: Installed plugins: []  
[2025-06-02 00:19:44] local.INFO: Installed plugins: []  
[2025-06-02 00:20:44] local.INFO: Installed plugins: []  
[2025-06-02 00:20:44] local.INFO: Installed plugins: []  
[2025-06-02 00:21:44] local.INFO: Installed plugins: []  
[2025-06-02 00:22:44] local.INFO: Installed plugins: []  
[2025-06-02 00:22:44] local.INFO: Installed plugins: []  
[2025-06-02 00:23:44] local.INFO: Installed plugins: []  
[2025-06-02 00:24:44] local.INFO: Installed plugins: []  
[2025-06-02 00:25:40] local.INFO: Installed plugins: []  
[2025-06-02 00:25:44] local.INFO: Installed plugins: []  
[2025-06-02 00:26:44] local.INFO: Installed plugins: []  
[2025-06-02 00:26:44] local.INFO: Installed plugins: []  
[2025-06-02 00:27:44] local.INFO: Installed plugins: []  
[2025-06-02 00:27:44] local.INFO: Installed plugins: []  
[2025-06-02 00:28:44] local.INFO: Installed plugins: []  
[2025-06-02 00:29:44] local.INFO: Installed plugins: []  
[2025-06-02 00:29:44] local.INFO: Installed plugins: []  
[2025-06-02 00:30:44] local.INFO: Installed plugins: []  
[2025-06-02 00:30:44] local.INFO: Installed plugins: []  
[2025-06-02 00:31:44] local.INFO: Installed plugins: []  
[2025-06-02 00:31:44] local.INFO: Installed plugins: []  
[2025-06-02 00:32:44] local.INFO: Installed plugins: []  
[2025-06-02 00:32:44] local.INFO: Installed plugins: []  
[2025-06-02 00:33:44] local.INFO: Installed plugins: []  
[2025-06-02 00:34:44] local.INFO: Installed plugins: []  
[2025-06-02 00:34:44] local.INFO: Installed plugins: []  
[2025-06-02 00:35:40] local.INFO: Installed plugins: []  
[2025-06-02 00:35:44] local.INFO: Installed plugins: []  
[2025-06-02 00:36:44] local.INFO: Installed plugins: []  
[2025-06-02 00:36:44] local.INFO: Installed plugins: []  
[2025-06-02 00:37:44] local.INFO: Installed plugins: []  
[2025-06-02 00:38:44] local.INFO: Installed plugins: []  
[2025-06-02 00:38:44] local.INFO: Installed plugins: []  
[2025-06-02 00:39:44] local.INFO: Installed plugins: []  
[2025-06-02 00:39:44] local.INFO: Installed plugins: []  
[2025-06-02 00:40:44] local.INFO: Installed plugins: []  
[2025-06-02 00:41:44] local.INFO: Installed plugins: []  
[2025-06-02 00:41:44] local.INFO: Installed plugins: []  
[2025-06-02 00:42:44] local.INFO: Installed plugins: []  
[2025-06-02 00:42:44] local.INFO: Installed plugins: []  
[2025-06-02 00:43:44] local.INFO: Installed plugins: []  
[2025-06-02 00:44:44] local.INFO: Installed plugins: []  
[2025-06-02 00:45:40] local.INFO: Installed plugins: []  
[2025-06-02 00:45:44] local.INFO: Installed plugins: []  
[2025-06-02 00:45:44] local.INFO: Installed plugins: []  
[2025-06-02 00:46:44] local.INFO: Installed plugins: []  
[2025-06-02 00:47:44] local.INFO: Installed plugins: []  
[2025-06-02 00:47:44] local.INFO: Installed plugins: []  
[2025-06-02 00:48:44] local.INFO: Installed plugins: []  
[2025-06-02 00:48:44] local.INFO: Installed plugins: []  
[2025-06-02 00:49:44] local.INFO: Installed plugins: []  
[2025-06-02 00:49:44] local.INFO: Installed plugins: []  
[2025-06-02 00:50:44] local.INFO: Installed plugins: []  
[2025-06-02 00:50:44] local.INFO: Installed plugins: []  
[2025-06-02 00:51:44] local.INFO: Installed plugins: []  
[2025-06-02 00:51:44] local.INFO: Installed plugins: []  
[2025-06-02 00:52:44] local.INFO: Installed plugins: []  
[2025-06-02 00:52:44] local.INFO: Installed plugins: []  
[2025-06-02 00:53:44] local.INFO: Installed plugins: []  
[2025-06-02 00:53:44] local.INFO: Installed plugins: []  
[2025-06-02 00:54:44] local.INFO: Installed plugins: []  
[2025-06-02 00:54:44] local.INFO: Installed plugins: []  
[2025-06-02 00:55:40] local.INFO: Installed plugins: []  
[2025-06-02 00:55:44] local.INFO: Installed plugins: []  
[2025-06-02 00:55:44] local.INFO: Installed plugins: []  
[2025-06-02 00:56:44] local.INFO: Installed plugins: []  
[2025-06-02 00:57:44] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:21:53] local.INFO: Installed plugins: []  
[2025-06-02 09:22:09] local.INFO: Installed plugins: []  
[2025-06-02 09:22:12] local.INFO: Installed plugins: []  
[2025-06-02 09:22:14] local.INFO: Installed plugins: []  
[2025-06-02 09:22:21] local.INFO: Installed plugins: []  
[2025-06-02 09:22:21] local.INFO: Installed plugins: []  
[2025-06-02 09:23:21] local.INFO: Installed plugins: []  
[2025-06-02 09:23:21] local.INFO: Installed plugins: []  
[2025-06-02 09:24:40] local.INFO: Installed plugins: []  
[2025-06-02 09:24:40] local.INFO: Installed plugins: []  
[2025-06-02 09:25:12] local.INFO: Installed plugins: []  
[2025-06-02 09:25:21] local.INFO: Installed plugins: []  
[2025-06-02 09:25:21] local.INFO: Installed plugins: []  
[2025-06-02 09:25:50] local.INFO: Installed plugins: []  
[2025-06-02 09:25:51] local.INFO: Installed plugins: []  
[2025-06-02 09:25:52] local.INFO: Installed plugins: []  
[2025-06-02 09:26:21] local.INFO: Installed plugins: []  
[2025-06-02 09:26:21] local.INFO: Installed plugins: []  
[2025-06-02 09:26:28] local.INFO: Installed plugins: []  
[2025-06-02 09:26:58] local.INFO: Installed plugins: []  
[2025-06-02 09:26:58] local.INFO: Installed plugins: []  
[2025-06-02 09:26:58] local.INFO: Installed plugins: []  
[2025-06-02 09:26:58] local.INFO: Installed plugins: []  
[2025-06-02 09:26:59] local.INFO: Installed plugins: []  
[2025-06-02 09:27:02] local.INFO: Installed plugins: []  
[2025-06-02 09:27:03] local.INFO: Installed plugins: []  
[2025-06-02 09:27:03] local.INFO: Installed plugins: []  
[2025-06-02 09:27:04] local.INFO: Installed plugins: []  
[2025-06-02 09:27:09] local.INFO: Installed plugins: []  
[2025-06-02 09:27:14] local.INFO: Installed plugins: []  
[2025-06-02 09:27:21] local.INFO: Installed plugins: []  
[2025-06-02 09:27:21] local.INFO: Installed plugins: []  
[2025-06-02 09:28:21] local.INFO: Installed plugins: []  
[2025-06-02 09:28:21] local.INFO: Installed plugins: []  
[2025-06-02 09:29:21] local.INFO: Installed plugins: []  
[2025-06-02 09:29:21] local.INFO: Installed plugins: []  
[2025-06-02 09:30:21] local.INFO: Installed plugins: []  
[2025-06-02 09:30:22] local.INFO: Installed plugins: []  
[2025-06-02 09:30:29] local.INFO: Installed plugins: []  
[2025-06-02 09:30:31] local.INFO: Installed plugins: []  
[2025-06-02 09:31:21] local.INFO: Installed plugins: []  
[2025-06-02 09:31:21] local.INFO: Installed plugins: []  
[2025-06-02 09:31:21] local.INFO: Installed plugins: []  
[2025-06-02 09:31:53] local.INFO: Installed plugins: []  
[2025-06-02 09:31:57] local.INFO: Installed plugins: []  
[2025-06-02 09:32:03] local.INFO: Installed plugins: []  
[2025-06-02 09:32:06] local.INFO: Installed plugins: []  
[2025-06-02 09:32:21] local.INFO: Installed plugins: []  
[2025-06-02 09:32:21] local.INFO: Installed plugins: []  
[2025-06-02 09:32:31] local.INFO: Installed plugins: []  
[2025-06-02 09:32:35] local.INFO: Installed plugins: []  
[2025-06-02 09:32:38] local.INFO: Installed plugins: []  
[2025-06-02 09:32:53] local.INFO: Installed plugins: []  
[2025-06-02 09:32:54] local.ERROR: Select field [mountedActionsData.0.department_id] must have a [createOptionUsing()] closure set. {"userId":2,"exception":"[object] (Exception(code: 0): Select field [mountedActionsData.0.department_id] must have a [createOptionUsing()] closure set. at E:\\projects\\My\\sucos\\vendor\\filament\\forms\\src\\Components\\Select.php:287)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Select::{closure:Filament\\Forms\\Components\\Select::getCreateOptionAction():285}(Object(Filament\\Forms\\Components\\Actions\\Action), Array, Object(Filament\\Forms\\Components\\Select), Array, Object(Filament\\Forms\\Form))
#1 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#2 E:\\projects\\My\\sucos\\vendor\\filament\\forms\\src\\Concerns\\HasFormComponentActions.php(79): Filament\\Actions\\MountableAction->call(Array)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedFormComponentAction(Array)
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#7 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#8 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedForm...', Array)
#9 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\PersonResource\\Pages\\ListPerson), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#10 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#11 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#12 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#21 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#23 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#63 {main}
"} 
[2025-06-02 09:33:21] local.INFO: Installed plugins: []  
[2025-06-02 09:34:07] local.INFO: Installed plugins: []  
[2025-06-02 09:34:16] local.INFO: Installed plugins: []  
[2025-06-02 09:34:18] local.INFO: Installed plugins: []  
[2025-06-02 09:34:21] local.INFO: Installed plugins: []  
[2025-06-02 09:34:29] local.INFO: Installed plugins: []  
[2025-06-02 09:34:30] local.INFO: Installed plugins: []  
[2025-06-02 09:34:37] local.INFO: Installed plugins: []  
[2025-06-02 09:34:39] local.INFO: Installed plugins: []  
[2025-06-02 09:34:43] local.INFO: Installed plugins: []  
[2025-06-02 09:34:46] local.INFO: Installed plugins: []  
[2025-06-02 09:34:50] local.INFO: Installed plugins: []  
[2025-06-02 09:35:01] local.INFO: Installed plugins: []  
[2025-06-02 09:35:01] local.INFO: Installed plugins: []  
[2025-06-02 09:35:03] local.INFO: Installed plugins: []  
[2025-06-02 09:35:07] local.INFO: Installed plugins: []  
[2025-06-02 09:35:09] local.INFO: Installed plugins: []  
[2025-06-02 09:35:21] local.INFO: Installed plugins: []  
[2025-06-02 09:35:21] local.INFO: Installed plugins: []  
[2025-06-02 09:35:26] local.INFO: Installed plugins: []  
[2025-06-02 09:35:32] local.INFO: Installed plugins: []  
[2025-06-02 09:35:33] local.INFO: Installed plugins: []  
[2025-06-02 09:36:13] local.INFO: Installed plugins: []  
[2025-06-02 09:36:16] local.INFO: Installed plugins: []  
[2025-06-02 09:36:19] local.INFO: Installed plugins: []  
[2025-06-02 09:36:21] local.INFO: Installed plugins: []  
[2025-06-02 09:36:33] local.INFO: Installed plugins: []  
[2025-06-02 09:36:35] local.INFO: Installed plugins: []  
[2025-06-02 09:37:21] local.INFO: Installed plugins: []  
[2025-06-02 09:37:21] local.INFO: Installed plugins: []  
[2025-06-02 09:38:21] local.INFO: Installed plugins: []  
[2025-06-02 09:38:21] local.INFO: Installed plugins: []  
[2025-06-02 09:38:26] local.INFO: Installed plugins: []  
[2025-06-02 09:38:30] local.INFO: Installed plugins: []  
[2025-06-02 09:39:21] local.INFO: Installed plugins: []  
[2025-06-02 09:39:21] local.INFO: Installed plugins: []  
[2025-06-02 09:39:40] local.INFO: Installed plugins: []  
[2025-06-02 09:39:43] local.INFO: Installed plugins: []  
[2025-06-02 09:40:21] local.INFO: Installed plugins: []  
[2025-06-02 09:40:27] local.INFO: Installed plugins: []  
[2025-06-02 09:40:32] local.INFO: Installed plugins: []  
[2025-06-02 09:40:41] local.INFO: Installed plugins: []  
[2025-06-02 09:40:44] local.INFO: Installed plugins: []  
[2025-06-02 09:40:48] local.INFO: Installed plugins: []  
[2025-06-02 09:40:57] local.INFO: Installed plugins: []  
[2025-06-02 09:41:06] local.INFO: Installed plugins: []  
[2025-06-02 09:41:06] local.INFO: Installed plugins: []  
[2025-06-02 09:41:21] local.INFO: Installed plugins: []  
[2025-06-02 09:41:21] local.INFO: Installed plugins: []  
[2025-06-02 09:41:27] local.INFO: Installed plugins: []  
[2025-06-02 09:41:28] local.INFO: Installed plugins: []  
[2025-06-02 09:41:33] local.INFO: Installed plugins: []  
[2025-06-02 09:41:37] local.INFO: Installed plugins: []  
[2025-06-02 09:41:39] local.INFO: Installed plugins: []  
[2025-06-02 09:41:41] local.INFO: Installed plugins: []  
[2025-06-02 09:41:47] local.INFO: Installed plugins: []  
[2025-06-02 09:41:48] local.INFO: Installed plugins: []  
[2025-06-02 09:41:48] local.INFO: Installed plugins: []  
[2025-06-02 09:41:48] local.INFO: Installed plugins: []  
[2025-06-02 09:41:49] local.ERROR: SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select "person_id", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from "sales" where "paid" = 0 and (strftime('%m', "sale_date") = cast(05 as text) and strftime('%Y', "sale_date") = cast(2025 as text)) group by "person_id") as "aggregate_table") {"view":{"view":"E:\\projects\\My\\sucos\\vendor\\filament\\tables\\resources\\views\\index.blade.php","data":[]},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select \"person_id\", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from \"sales\" where \"paid\" = 0 and (strftime('%m', \"sale_date\") = cast(05 as text) and strftime('%Y', \"sale_date\") = cast(2025 as text)) group by \"person_id\") as \"aggregate_table\") at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\resources\\views\\index.blade.php(66): Filament\\Tables\\Table->getRecords()
#12 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#13 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#19 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\resources\\views\\resources\\pages\\list-records.blade.php(14): e(Object(Filament\\Tables\\Table))
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#29 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#96 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select \"person_id\", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from \"sales\" where \"paid\" = 0 and (strftime('%m', \"sale_date\") = cast(05 as text) and strftime('%Y', \"sale_date\") = cast(2025 as text)) group by \"person_id\") as \"aggregate_table\") at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 E:\\projects\\My\\sucos\\storage\\framework\\views\\ef16234c06be887a7842299a629e2673.php(66): Filament\\Tables\\Table->getRecords()
#12 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#13 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#19 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 E:\\projects\\My\\sucos\\storage\\framework\\views\\7c2d77cb7c7bab279afacabc6e1bb909.php(42): e(Object(Filament\\Tables\\Table))
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#29 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#96 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select count(*)...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('select count(*)...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#12 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#13 E:\\projects\\My\\sucos\\storage\\framework\\views\\ef16234c06be887a7842299a629e2673.php(66): Filament\\Tables\\Table->getRecords()
#14 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#21 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 E:\\projects\\My\\sucos\\storage\\framework\\views\\7c2d77cb7c7bab279afacabc6e1bb909.php(42): e(Object(Filament\\Tables\\Table))
#24 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#38 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#39 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#95 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#96 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#97 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#98 {main}
"} 
[2025-06-02 09:42:03] local.INFO: Installed plugins: []  
[2025-06-02 09:42:11] local.INFO: Installed plugins: []  
[2025-06-02 09:42:14] local.INFO: Installed plugins: []  
[2025-06-02 09:42:18] local.INFO: Installed plugins: []  
[2025-06-02 09:42:19] local.INFO: Installed plugins: []  
[2025-06-02 09:42:21] local.INFO: Installed plugins: []  
[2025-06-02 09:42:21] local.INFO: Installed plugins: []  
[2025-06-02 09:42:37] local.INFO: Installed plugins: []  
[2025-06-02 09:43:07] local.INFO: Installed plugins: []  
[2025-06-02 09:43:09] local.INFO: Installed plugins: []  
[2025-06-02 09:43:11] local.INFO: Installed plugins: []  
[2025-06-02 09:43:13] local.INFO: Installed plugins: []  
[2025-06-02 09:43:14] local.INFO: Installed plugins: []  
[2025-06-02 09:43:15] local.INFO: Installed plugins: []  
[2025-06-02 09:43:21] local.INFO: Installed plugins: []  
[2025-06-02 09:43:37] local.INFO: Installed plugins: []  
[2025-06-02 09:43:39] local.INFO: Installed plugins: []  
[2025-06-02 09:44:21] local.INFO: Installed plugins: []  
[2025-06-02 09:44:21] local.INFO: Installed plugins: []  
[2025-06-02 09:44:34] local.INFO: Installed plugins: []  
[2025-06-02 09:44:36] local.INFO: Installed plugins: []  
[2025-06-02 09:44:39] local.INFO: Installed plugins: []  
[2025-06-02 09:44:59] local.INFO: Installed plugins: []  
[2025-06-02 09:45:21] local.INFO: Installed plugins: []  
[2025-06-02 09:45:36] local.INFO: Installed plugins: []  
[2025-06-02 09:45:41] local.INFO: Installed plugins: []  
[2025-06-02 09:45:44] local.INFO: Installed plugins: []  
[2025-06-02 09:46:04] local.INFO: Installed plugins: []  
[2025-06-02 09:46:05] local.INFO: Installed plugins: []  
[2025-06-02 09:46:06] local.INFO: Installed plugins: []  
[2025-06-02 09:46:09] local.INFO: Installed plugins: []  
[2025-06-02 09:46:10] local.INFO: Installed plugins: []  
[2025-06-02 09:46:11] local.INFO: Installed plugins: []  
[2025-06-02 09:46:15] local.INFO: Installed plugins: []  
[2025-06-02 09:46:17] local.INFO: Installed plugins: []  
[2025-06-02 09:46:21] local.INFO: Installed plugins: []  
[2025-06-02 09:46:21] local.INFO: Installed plugins: []  
[2025-06-02 09:46:26] local.INFO: Installed plugins: []  
[2025-06-02 09:46:38] local.INFO: Installed plugins: []  
[2025-06-02 09:46:48] local.INFO: Installed plugins: []  
[2025-06-02 09:46:50] local.INFO: Installed plugins: []  
[2025-06-02 09:46:53] local.INFO: Installed plugins: []  
[2025-06-02 09:47:21] local.INFO: Installed plugins: []  
[2025-06-02 09:47:21] local.INFO: Installed plugins: []  
[2025-06-02 09:48:21] local.INFO: Installed plugins: []  
[2025-06-02 09:48:21] local.INFO: Installed plugins: []  
[2025-06-02 09:49:21] local.INFO: Installed plugins: []  
[2025-06-02 09:50:21] local.INFO: Installed plugins: []  
[2025-06-02 09:50:21] local.INFO: Installed plugins: []  
[2025-06-02 09:51:21] local.INFO: Installed plugins: []  
[2025-06-02 09:51:21] local.INFO: Installed plugins: []  
[2025-06-02 09:52:33] local.INFO: Installed plugins: []  
[2025-06-02 09:52:33] local.INFO: Installed plugins: []  
[2025-06-02 09:53:21] local.INFO: Installed plugins: []  
[2025-06-02 09:54:21] local.INFO: Installed plugins: []  
[2025-06-02 09:54:21] local.INFO: Installed plugins: []  
[2025-06-02 09:55:21] local.INFO: Installed plugins: []  
[2025-06-02 09:55:21] local.INFO: Installed plugins: []  
[2025-06-02 09:56:21] local.INFO: Installed plugins: []  
[2025-06-02 09:56:21] local.INFO: Installed plugins: []  
[2025-06-02 09:56:51] local.INFO: Installed plugins: []  
[2025-06-02 09:57:21] local.INFO: Installed plugins: []  
[2025-06-02 09:57:21] local.INFO: Installed plugins: []  
[2025-06-02 09:58:21] local.INFO: Installed plugins: []  
[2025-06-02 09:58:21] local.INFO: Installed plugins: []  
[2025-06-02 09:59:21] local.INFO: Installed plugins: []  
[2025-06-02 10:00:21] local.INFO: Installed plugins: []  
[2025-06-02 10:00:21] local.INFO: Installed plugins: []  
[2025-06-02 10:01:21] local.INFO: Installed plugins: []  
[2025-06-02 10:01:21] local.INFO: Installed plugins: []  
[2025-06-02 10:01:21] local.INFO: Installed plugins: []  
[2025-06-02 10:02:21] local.INFO: Installed plugins: []  
[2025-06-02 10:03:21] local.INFO: Installed plugins: []  
[2025-06-02 10:03:21] local.INFO: Installed plugins: []  
[2025-06-02 10:04:21] local.INFO: Installed plugins: []  
[2025-06-02 10:05:21] local.INFO: Installed plugins: []  
[2025-06-02 10:06:21] local.INFO: Installed plugins: []  
[2025-06-02 10:07:21] local.INFO: Installed plugins: []  
[2025-06-02 10:07:21] local.INFO: Installed plugins: []  
[2025-06-02 10:08:21] local.INFO: Installed plugins: []  
[2025-06-02 10:09:21] local.INFO: Installed plugins: []  
[2025-06-02 10:10:21] local.INFO: Installed plugins: []  
[2025-06-02 10:10:21] local.INFO: Installed plugins: []  
[2025-06-02 10:11:21] local.INFO: Installed plugins: []  
[2025-06-02 10:11:21] local.INFO: Installed plugins: []  
[2025-06-02 10:11:21] local.INFO: Installed plugins: []  
[2025-06-02 10:12:21] local.INFO: Installed plugins: []  
[2025-06-02 10:12:21] local.INFO: Installed plugins: []  
[2025-06-02 10:13:21] local.INFO: Installed plugins: []  
[2025-06-02 10:13:21] local.INFO: Installed plugins: []  
[2025-06-02 10:14:21] local.INFO: Installed plugins: []  
[2025-06-02 10:15:21] local.INFO: Installed plugins: []  
[2025-06-02 10:15:21] local.INFO: Installed plugins: []  
[2025-06-02 10:16:21] local.INFO: Installed plugins: []  
[2025-06-02 10:16:21] local.INFO: Installed plugins: []  
[2025-06-02 10:17:21] local.INFO: Installed plugins: []  
[2025-06-02 10:18:21] local.INFO: Installed plugins: []  
[2025-06-02 10:18:21] local.INFO: Installed plugins: []  
[2025-06-02 10:19:21] local.INFO: Installed plugins: []  
[2025-06-02 10:20:21] local.INFO: Installed plugins: []  
[2025-06-02 10:21:21] local.INFO: Installed plugins: []  
[2025-06-02 10:21:21] local.INFO: Installed plugins: []  
[2025-06-02 10:21:21] local.INFO: Installed plugins: []  
[2025-06-02 10:22:21] local.INFO: Installed plugins: []  
[2025-06-02 10:23:21] local.INFO: Installed plugins: []  
[2025-06-02 10:24:21] local.INFO: Installed plugins: []  
[2025-06-02 10:25:21] local.INFO: Installed plugins: []  
[2025-06-02 10:26:21] local.INFO: Installed plugins: []  
[2025-06-02 10:27:21] local.INFO: Installed plugins: []  
[2025-06-02 10:27:21] local.INFO: Installed plugins: []  
[2025-06-02 10:28:21] local.INFO: Installed plugins: []  
[2025-06-02 10:28:21] local.INFO: Installed plugins: []  
[2025-06-02 10:29:21] local.INFO: Installed plugins: []  
[2025-06-02 10:29:21] local.INFO: Installed plugins: []  
[2025-06-02 10:30:21] local.INFO: Installed plugins: []  
[2025-06-02 10:31:21] local.INFO: Installed plugins: []  
[2025-06-02 10:31:21] local.INFO: Installed plugins: []  
[2025-06-02 10:31:21] local.INFO: Installed plugins: []  
[2025-06-02 10:32:21] local.INFO: Installed plugins: []  
[2025-06-02 10:33:21] local.INFO: Installed plugins: []  
[2025-06-02 10:33:21] local.INFO: Installed plugins: []  
[2025-06-02 10:34:21] local.INFO: Installed plugins: []  
[2025-06-02 10:35:21] local.INFO: Installed plugins: []  
[2025-06-02 10:35:21] local.INFO: Installed plugins: []  
[2025-06-02 10:36:21] local.INFO: Installed plugins: []  
[2025-06-02 10:36:21] local.INFO: Installed plugins: []  
[2025-06-02 10:37:21] local.INFO: Installed plugins: []  
[2025-06-02 10:38:21] local.INFO: Installed plugins: []  
[2025-06-02 10:38:21] local.INFO: Installed plugins: []  
[2025-06-02 10:39:21] local.INFO: Installed plugins: []  
[2025-06-02 10:40:21] local.INFO: Installed plugins: []  
[2025-06-02 10:41:21] local.INFO: Installed plugins: []  
[2025-06-02 10:41:21] local.INFO: Installed plugins: []  
[2025-06-02 10:42:21] local.INFO: Installed plugins: []  
[2025-06-02 10:42:21] local.INFO: Installed plugins: []  
[2025-06-02 10:43:21] local.INFO: Installed plugins: []  
[2025-06-02 10:44:21] local.INFO: Installed plugins: []  
[2025-06-02 10:44:21] local.INFO: Installed plugins: []  
[2025-06-02 10:45:21] local.INFO: Installed plugins: []  
[2025-06-02 10:45:21] local.INFO: Installed plugins: []  
[2025-06-02 10:46:21] local.INFO: Installed plugins: []  
[2025-06-02 10:46:21] local.INFO: Installed plugins: []  
[2025-06-02 10:47:21] local.INFO: Installed plugins: []  
[2025-06-02 10:48:21] local.INFO: Installed plugins: []  
[2025-06-02 10:49:21] local.INFO: Installed plugins: []  
[2025-06-02 10:49:21] local.INFO: Installed plugins: []  
[2025-06-02 10:50:21] local.INFO: Installed plugins: []  
[2025-06-02 10:50:21] local.INFO: Installed plugins: []  
[2025-06-02 10:51:21] local.INFO: Installed plugins: []  
[2025-06-02 10:51:21] local.INFO: Installed plugins: []  
[2025-06-02 10:51:21] local.INFO: Installed plugins: []  
[2025-06-02 10:52:21] local.INFO: Installed plugins: []  
[2025-06-02 10:52:21] local.INFO: Installed plugins: []  
[2025-06-02 10:53:21] local.INFO: Installed plugins: []  
[2025-06-02 10:53:21] local.INFO: Installed plugins: []  
[2025-06-02 10:54:21] local.INFO: Installed plugins: []  
[2025-06-02 10:54:21] local.INFO: Installed plugins: []  
[2025-06-02 10:55:21] local.INFO: Installed plugins: []  
[2025-06-02 10:56:21] local.INFO: Installed plugins: []  
[2025-06-02 10:57:21] local.INFO: Installed plugins: []  
[2025-06-02 10:58:21] local.INFO: Installed plugins: []  
[2025-06-02 10:59:21] local.INFO: Installed plugins: []  
[2025-06-02 10:59:21] local.INFO: Installed plugins: []  
[2025-06-02 11:00:21] local.INFO: Installed plugins: []  
[2025-06-02 11:01:21] local.INFO: Installed plugins: []  
[2025-06-02 11:01:21] local.INFO: Installed plugins: []  
[2025-06-02 11:01:21] local.INFO: Installed plugins: []  
[2025-06-02 11:02:21] local.INFO: Installed plugins: []  
[2025-06-02 11:02:21] local.INFO: Installed plugins: []  
[2025-06-02 11:03:21] local.INFO: Installed plugins: []  
[2025-06-02 11:03:21] local.INFO: Installed plugins: []  
[2025-06-02 11:04:21] local.INFO: Installed plugins: []  
[2025-06-02 11:05:21] local.INFO: Installed plugins: []  
[2025-06-02 11:06:21] local.INFO: Installed plugins: []  
[2025-06-02 11:06:21] local.INFO: Installed plugins: []  
[2025-06-02 11:07:21] local.INFO: Installed plugins: []  
[2025-06-02 11:09:55] local.INFO: Installed plugins: []  
[2025-06-02 11:10:01] local.INFO: Installed plugins: []  
[2025-06-02 11:10:04] local.INFO: Installed plugins: []  
[2025-06-02 11:10:08] local.INFO: Installed plugins: []  
[2025-06-02 11:10:11] local.INFO: Installed plugins: []  
[2025-06-02 11:10:13] local.INFO: Installed plugins: []  
[2025-06-02 11:10:14] local.INFO: Installed plugins: []  
[2025-06-02 11:10:14] local.INFO: Installed plugins: []  
[2025-06-02 11:10:16] local.INFO: Installed plugins: []  
[2025-06-02 11:10:20] local.INFO: Installed plugins: []  
[2025-06-02 11:10:22] local.INFO: Installed plugins: []  
[2025-06-02 11:10:23] local.INFO: Installed plugins: []  
[2025-06-02 11:10:23] local.INFO: Installed plugins: []  
[2025-06-02 11:10:27] local.INFO: Installed plugins: []  
[2025-06-02 11:10:39] local.INFO: Installed plugins: []  
[2025-06-02 11:10:44] local.INFO: Installed plugins: []  
[2025-06-02 11:10:47] local.INFO: Installed plugins: []  
[2025-06-02 11:10:52] local.INFO: Installed plugins: []  
[2025-06-02 11:11:02] local.INFO: Installed plugins: []  
[2025-06-02 11:11:07] local.INFO: Installed plugins: []  
[2025-06-02 11:11:12] local.INFO: Installed plugins: []  
[2025-06-02 11:11:17] local.INFO: Installed plugins: []  
[2025-06-02 11:11:22] local.INFO: Installed plugins: []  
[2025-06-02 11:11:22] local.INFO: Installed plugins: []  
[2025-06-02 11:11:23] local.INFO: Installed plugins: []  
[2025-06-02 11:11:27] local.INFO: Installed plugins: []  
[2025-06-02 11:11:27] local.INFO: Installed plugins: []  
[2025-06-02 11:11:29] local.INFO: Installed plugins: []  
[2025-06-02 11:11:29] local.INFO: Installed plugins: []  
[2025-06-02 11:11:29] local.INFO: Installed plugins: []  
[2025-06-02 11:11:29] local.INFO: Installed plugins: []  
[2025-06-02 11:11:30] local.INFO: Installed plugins: []  
[2025-06-02 11:11:35] local.INFO: Installed plugins: []  
[2025-06-02 11:11:40] local.INFO: Installed plugins: []  
[2025-06-02 11:11:45] local.INFO: Installed plugins: []  
[2025-06-02 11:11:50] local.INFO: Installed plugins: []  
[2025-06-02 11:11:55] local.INFO: Installed plugins: []  
[2025-06-02 11:12:00] local.INFO: Installed plugins: []  
[2025-06-02 11:12:05] local.INFO: Installed plugins: []  
[2025-06-02 11:12:12] local.INFO: Installed plugins: []  
[2025-06-02 11:12:17] local.INFO: Installed plugins: []  
[2025-06-02 11:12:37] local.INFO: Installed plugins: []  
[2025-06-02 11:12:45] local.INFO: Installed plugins: []  
[2025-06-02 11:12:47] local.INFO: Installed plugins: []  
[2025-06-02 11:13:02] local.INFO: Installed plugins: []  
[2025-06-02 11:13:05] local.INFO: Installed plugins: []  
[2025-06-02 11:13:09] local.INFO: Installed plugins: []  
[2025-06-02 11:13:10] local.INFO: Installed plugins: []  
[2025-06-02 11:13:10] local.INFO: Installed plugins: []  
[2025-06-02 11:13:24] local.INFO: Installed plugins: []  
[2025-06-02 11:13:25] local.INFO: Installed plugins: []  
[2025-06-02 11:13:25] local.INFO: Installed plugins: []  
[2025-06-02 11:13:25] local.INFO: Installed plugins: []  
[2025-06-02 11:13:26] local.INFO: Installed plugins: []  
[2025-06-02 11:13:31] local.INFO: Installed plugins: []  
[2025-06-02 11:13:36] local.INFO: Installed plugins: []  
[2025-06-02 11:13:41] local.INFO: Installed plugins: []  
[2025-06-02 11:13:44] local.INFO: Installed plugins: []  
[2025-06-02 11:13:45] local.INFO: Installed plugins: []  
[2025-06-02 11:13:48] local.INFO: Installed plugins: []  
[2025-06-02 11:14:02] local.INFO: Installed plugins: []  
[2025-06-02 11:14:11] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:12] local.INFO: Installed plugins: []  
[2025-06-02 11:14:21] local.INFO: Installed plugins: []  
[2025-06-02 11:14:21] local.INFO: Installed plugins: []  
[2025-06-02 11:14:26] local.INFO: Installed plugins: []  
[2025-06-02 11:17:17] local.INFO: Installed plugins: []  
[2025-06-02 11:17:26] local.INFO: Installed plugins: []  
[2025-06-02 14:27:11] local.INFO: Installed plugins: []  
[2025-06-02 14:27:52] local.INFO: Installed plugins: []  
[2025-06-02 14:27:53] local.INFO: Installed plugins: []  
[2025-06-02 14:27:54] local.INFO: Installed plugins: []  
[2025-06-02 14:44:34] local.INFO: Installed plugins: []  
[2025-06-02 14:44:47] local.INFO: Installed plugins: []  
[2025-06-02 14:44:47] local.INFO: Installed plugins: []  
[2025-06-02 14:44:47] local.INFO: Installed plugins: []  
[2025-06-02 14:44:47] local.INFO: Installed plugins: []  
[2025-06-02 14:44:49] local.INFO: Installed plugins: []  
[2025-06-02 14:44:54] local.INFO: Installed plugins: []  
[2025-06-02 14:45:01] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:08] local.INFO: Installed plugins: []  
[2025-06-02 14:45:09] local.INFO: Installed plugins: []  
[2025-06-02 14:45:14] local.INFO: Installed plugins: []  
[2025-06-02 14:45:16] local.INFO: Installed plugins: []  
[2025-06-02 14:45:16] local.INFO: Installed plugins: []  
[2025-06-02 14:45:20] local.INFO: Installed plugins: []  
[2025-06-02 14:45:22] local.INFO: Installed plugins: []  
[2025-06-02 14:45:36] local.INFO: Installed plugins: []  
[2025-06-02 14:45:46] local.INFO: Installed plugins: []  
[2025-06-02 14:45:56] local.INFO: Installed plugins: []  
[2025-06-02 14:46:07] local.INFO: Installed plugins: []  
[2025-06-02 14:46:07] local.INFO: Installed plugins: []  
[2025-06-02 14:46:09] local.INFO: Installed plugins: []  
[2025-06-02 14:46:14] local.INFO: Installed plugins: []  
[2025-06-02 14:46:19] local.INFO: Installed plugins: []  
[2025-06-02 14:46:24] local.INFO: Installed plugins: []  
[2025-06-02 14:46:29] local.INFO: Installed plugins: []  
[2025-06-02 14:46:34] local.INFO: Installed plugins: []  
[2025-06-02 14:46:39] local.INFO: Installed plugins: []  
[2025-06-02 14:46:40] local.INFO: Installed plugins: []  
[2025-06-02 14:46:49] local.ERROR: SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select "person_id", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from "sales" where "paid" = 0 and (strftime('%m', "sale_date") = cast(05 as text) and strftime('%Y', "sale_date") = cast(2025 as text)) group by "person_id") as "aggregate_table") {"view":{"view":"E:\\projects\\My\\sucos\\vendor\\filament\\tables\\resources\\views\\index.blade.php","data":[]},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select \"person_id\", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from \"sales\" where \"paid\" = 0 and (strftime('%m', \"sale_date\") = cast(05 as text) and strftime('%Y', \"sale_date\") = cast(2025 as text)) group by \"person_id\") as \"aggregate_table\") at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\resources\\views\\index.blade.php(66): Filament\\Tables\\Table->getRecords()
#12 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#13 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#19 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\resources\\views\\resources\\pages\\list-records.blade.php(14): e(Object(Filament\\Tables\\Table))
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#29 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#96 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE (Connection: sqlite, SQL: select count(*) as aggregate from (select \"person_id\", ANY_VALUE(id) as id, SUM(quantity) as quantity, SUM(total) as total, EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests from \"sales\" where \"paid\" = 0 and (strftime('%m', \"sale_date\") = cast(05 as text) and strftime('%Y', \"sale_date\") = cast(2025 as text)) group by \"person_id\") as \"aggregate_table\") at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 E:\\projects\\My\\sucos\\storage\\framework\\views\\ef16234c06be887a7842299a629e2673.php(66): Filament\\Tables\\Table->getRecords()
#12 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#13 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#19 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 E:\\projects\\My\\sucos\\storage\\framework\\views\\7c2d77cb7c7bab279afacabc6e1bb909.php(42): e(Object(Filament\\Tables\\Table))
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#29 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#96 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: ANY_VALUE at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select count(*)...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('select count(*)...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3277): Illuminate\\Database\\Query\\Builder->get()
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3244): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#10 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#12 E:\\projects\\My\\sucos\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#13 E:\\projects\\My\\sucos\\storage\\framework\\views\\ef16234c06be887a7842299a629e2673.php(66): Filament\\Tables\\Table->getRecords()
#14 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#15 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#17 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#21 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 E:\\projects\\My\\sucos\\storage\\framework\\views\\7c2d77cb7c7bab279afacabc6e1bb909.php(42): e(Object(Filament\\Tables\\Table))
#24 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('E:\\\\projects\\\\My\\\\...')
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#27 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#31 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#32 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), Object(Closure))
#34 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '<div></div>')
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#36 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#37 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#38 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#39 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ChargeResource\\Pages\\ListCharges), '__invoke')
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#80 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#83 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#86 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#88 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#90 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#92 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#94 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#95 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#96 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#97 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#98 {main}
"} 
[2025-06-02 14:46:51] local.INFO: Installed plugins: []  
[2025-06-02 14:47:07] local.INFO: Installed plugins: []  
[2025-06-02 14:47:07] local.INFO: Installed plugins: []  
[2025-06-02 14:47:31] local.INFO: Installed plugins: []  
[2025-06-02 14:48:07] local.INFO: Installed plugins: []  
[2025-06-02 14:48:58] local.INFO: Installed plugins: []  
[2025-06-02 14:48:59] local.INFO: Installed plugins: []  
[2025-06-02 14:49:07] local.INFO: Installed plugins: []  
[2025-06-02 14:49:07] local.INFO: Installed plugins: []  
[2025-06-02 14:49:34] local.INFO: Installed plugins: []  
[2025-06-02 14:50:07] local.INFO: Installed plugins: []  
[2025-06-02 14:51:07] local.INFO: Installed plugins: []  
[2025-06-02 14:51:07] local.INFO: Installed plugins: []  
[2025-06-02 14:52:07] local.INFO: Installed plugins: []  
[2025-06-02 14:53:07] local.INFO: Installed plugins: []  
[2025-06-02 14:53:07] local.INFO: Installed plugins: []  
[2025-06-02 14:53:58] local.INFO: Installed plugins: []  
[2025-06-02 14:54:07] local.INFO: Installed plugins: []  
[2025-06-02 14:54:07] local.INFO: Installed plugins: []  
[2025-06-02 14:55:07] local.INFO: Installed plugins: []  
[2025-06-02 14:55:07] local.INFO: Installed plugins: []  
[2025-06-02 14:55:07] local.INFO: Installed plugins: []  
[2025-06-02 14:56:07] local.INFO: Installed plugins: []  
[2025-06-02 14:57:07] local.INFO: Installed plugins: []  
[2025-06-02 14:57:07] local.INFO: Installed plugins: []  
[2025-06-02 14:58:07] local.INFO: Installed plugins: []  
[2025-06-02 14:58:58] local.INFO: Installed plugins: []  
[2025-06-02 14:59:07] local.INFO: Installed plugins: []  
[2025-06-02 14:59:07] local.INFO: Installed plugins: []  
[2025-06-02 15:00:07] local.INFO: Installed plugins: []  
[2025-06-02 15:00:07] local.INFO: Installed plugins: []  
[2025-06-02 15:01:07] local.INFO: Installed plugins: []  
[2025-06-02 15:02:07] local.INFO: Installed plugins: []  
[2025-06-02 15:02:07] local.INFO: Installed plugins: []  
[2025-06-02 15:03:07] local.INFO: Installed plugins: []  
[2025-06-02 15:03:07] local.INFO: Installed plugins: []  
[2025-06-02 15:04:07] local.INFO: Installed plugins: []  
[2025-06-02 15:04:07] local.INFO: Installed plugins: []  
[2025-06-02 15:05:07] local.INFO: Installed plugins: []  
[2025-06-02 15:05:07] local.INFO: Installed plugins: []  
[2025-06-02 15:05:07] local.INFO: Installed plugins: []  
[2025-06-02 15:06:07] local.INFO: Installed plugins: []  
[2025-06-02 15:06:07] local.INFO: Installed plugins: []  
[2025-06-02 15:06:58] local.INFO: Installed plugins: []  
[2025-06-02 15:07:07] local.INFO: Installed plugins: []  
[2025-06-02 15:07:07] local.INFO: Installed plugins: []  
[2025-06-02 15:08:07] local.INFO: Installed plugins: []  
[2025-06-02 15:08:07] local.INFO: Installed plugins: []  
[2025-06-02 15:08:47] local.INFO: Installed plugins: []  
[2025-06-02 15:09:07] local.INFO: Installed plugins: []  
[2025-06-02 15:09:07] local.INFO: Installed plugins: []  
[2025-06-02 15:09:58] local.INFO: Installed plugins: []  
[2025-06-02 15:10:07] local.INFO: Installed plugins: []  
[2025-06-02 15:10:07] local.INFO: Installed plugins: []  
[2025-06-02 15:11:07] local.INFO: Installed plugins: []  
[2025-06-02 15:11:07] local.INFO: Installed plugins: []  
[2025-06-02 15:12:07] local.INFO: Installed plugins: []  
[2025-06-02 15:12:07] local.INFO: Installed plugins: []  
[2025-06-02 15:12:58] local.INFO: Installed plugins: []  
[2025-06-02 15:12:59] local.INFO: Installed plugins: []  
[2025-06-02 15:13:07] local.INFO: Installed plugins: []  
[2025-06-02 15:14:07] local.INFO: Installed plugins: []  
[2025-06-02 15:14:58] local.INFO: Installed plugins: []  
[2025-06-02 15:14:59] local.INFO: Installed plugins: []  
[2025-06-02 15:15:04] local.INFO: Installed plugins: []  
[2025-06-02 15:15:07] local.INFO: Installed plugins: []  
[2025-06-02 15:15:07] local.INFO: Installed plugins: []  
[2025-06-02 15:15:07] local.INFO: Installed plugins: []  
[2025-06-02 15:15:09] local.INFO: Installed plugins: []  
[2025-06-02 15:15:14] local.INFO: Installed plugins: []  
[2025-06-02 15:15:19] local.INFO: Installed plugins: []  
[2025-06-02 15:15:24] local.INFO: Installed plugins: []  
[2025-06-02 15:15:51] local.INFO: Installed plugins: []  
[2025-06-02 15:16:07] local.INFO: Installed plugins: []  
[2025-06-02 15:16:07] local.INFO: Installed plugins: []  
[2025-06-02 15:16:11] local.INFO: Installed plugins: []  
[2025-06-02 15:16:16] local.INFO: Installed plugins: []  
[2025-06-02 15:16:40] local.INFO: Installed plugins: []  
[2025-06-02 15:16:40] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist (Connection: mysql, SQL: select * from `users` where `id` = 2 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist (Connection: mysql, SQL: select * from `users` where `id` = 2 limit 1) at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(170): Illuminate\\Auth\\EloquentUserProvider->retrieveById(2)
#11 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Illuminate\\Auth\\SessionGuard->user()
#12 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php(19): Illuminate\\Auth\\SessionGuard->check()
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Filament\\Http\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Drawer\\Utils.php(182): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php(98): Livewire\\Drawer\\Utils::applyMiddleware(Object(Illuminate\\Http\\Request), Array)
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php(43): Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->applyPersistentMiddleware()
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->{closure:Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware::boot():37}(Array)
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('snapshot-verifi...', Array)
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(126): Livewire\\trigger('snapshot-verifi...', Array)
#24 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(92): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->fromSnapshot(Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#26 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#27 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#78 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select * from `...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('select * from `...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(170): Illuminate\\Auth\\EloquentUserProvider->retrieveById(2)
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Illuminate\\Auth\\SessionGuard->user()
#14 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php(19): Illuminate\\Auth\\SessionGuard->check()
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Filament\\Http\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Drawer\\Utils.php(182): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php(98): Livewire\\Drawer\\Utils::applyMiddleware(Object(Illuminate\\Http\\Request), Array)
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php(43): Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->applyPersistentMiddleware()
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->{closure:Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware::boot():37}(Array)
#24 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('snapshot-verifi...', Array)
#25 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(126): Livewire\\trigger('snapshot-verifi...', Array)
#26 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(92): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->fromSnapshot(Array)
#27 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#28 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#29 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#74 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#76 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#80 {main}
"} 
[2025-06-02 15:16:41] local.INFO: Installed plugins: []  
[2025-06-02 15:16:41] local.INFO: Installed plugins: []  
[2025-06-02 15:16:42] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist (Connection: mysql, SQL: select * from `users` where `id` = 2 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist (Connection: mysql, SQL: select * from `users` where `id` = 2 limit 1) at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(170): Illuminate\\Auth\\EloquentUserProvider->retrieveById(2)
#11 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Illuminate\\Auth\\SessionGuard->user()
#12 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php(19): Illuminate\\Auth\\SessionGuard->check()
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Filament\\Http\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#21 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#23 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.users' doesn't exist at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select * from `...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('select * from `...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3090}()
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(170): Illuminate\\Auth\\EloquentUserProvider->retrieveById(2)
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Illuminate\\Auth\\SessionGuard->user()
#14 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php(19): Illuminate\\Auth\\SessionGuard->check()
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Filament\\Http\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#20 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#23 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#58 {main}
"} 
[2025-06-02 15:16:42] local.INFO: Installed plugins: []  
[2025-06-02 15:17:04] local.ERROR: Error connecting to database: could not find driver  
[2025-06-02 15:17:07] local.INFO: Installed plugins: []  
[2025-06-02 15:17:07] local.INFO: Installed plugins: []  
[2025-06-02 15:17:08] local.INFO: Installed plugins: []  
[2025-06-02 15:17:26] local.ERROR: could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#4 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():165}()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): retry()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():110}()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#20 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#21 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#22 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#24 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:67)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): PDO::connect()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#16 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():165}()
#19 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): retry()
#20 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():110}()
#23 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#24 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#27 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#28 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#30 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#31 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#32 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#33 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#34 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#35 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#36 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#37 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#38 {main}
"} 
[2025-06-02 15:18:07] local.INFO: Installed plugins: []  
[2025-06-02 15:18:07] local.INFO: Installed plugins: []  
[2025-06-02 15:18:36] local.ERROR: Error connecting to database: could not find driver  
[2025-06-02 15:18:40] local.INFO: Installed plugins: []  
[2025-06-02 15:18:57] local.ERROR: could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#4 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():165}()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): retry()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():110}()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#20 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#21 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#22 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#24 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:67)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): PDO::connect()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#16 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():165}()
#19 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(165): retry()
#20 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():110}()
#23 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#24 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#27 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#28 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#30 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#31 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#32 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#33 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#34 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#35 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#36 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#37 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#38 {main}
"} 
[2025-06-02 15:19:07] local.INFO: Installed plugins: []  
[2025-06-02 15:19:07] local.INFO: Installed plugins: []  
[2025-06-02 15:19:50] local.ERROR: Error connecting to database: could not find driver  
[2025-06-02 15:19:54] local.INFO: Installed plugins: []  
[2025-06-02 15:20:07] local.INFO: Installed plugins: []  
[2025-06-02 15:20:07] local.INFO: Installed plugins: []  
[2025-06-02 15:20:13] local.ERROR: could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'sucos' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#4 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(56): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\StatusCommand->{closure:Illuminate\\Database\\Console\\Migrations\\StatusCommand::handle():55}()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(55): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#16 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#18 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#19 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#20 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#21 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#22 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:67)
[stacktrace]
#0 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): PDO::connect()
#1 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(344): Illuminate\\Database\\Connection->select()
#13 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Connection.php(361): Illuminate\\Database\\Connection->selectOne()
#14 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar()
#15 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#16 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(56): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\StatusCommand->{closure:Illuminate\\Database\\Console\\Migrations\\StatusCommand::handle():55}()
#19 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(55): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#20 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#21 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#23 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#25 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#26 /mnt/e/projects/My/sucos/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#27 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#28 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#29 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 /mnt/e/projects/My/sucos/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 /mnt/e/projects/My/sucos/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#32 /mnt/e/projects/My/sucos/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 {main}
"} 
[2025-06-02 15:21:07] local.INFO: Installed plugins: []  
[2025-06-02 15:21:07] local.INFO: Installed plugins: []  
[2025-06-02 15:21:26] local.INFO: Installed plugins: []  
[2025-06-02 15:21:51] local.INFO: Installed plugins: []  
[2025-06-02 15:21:52] local.INFO: Installed plugins: []  
[2025-06-02 15:21:53] local.INFO: Installed plugins: []  
[2025-06-02 15:21:57] local.INFO: Installed plugins: []  
[2025-06-02 15:22:04] local.INFO: Installed plugins: []  
[2025-06-02 15:22:05] local.INFO: Installed plugins: []  
[2025-06-02 15:22:06] local.INFO: Installed plugins: []  
[2025-06-02 15:22:07] local.INFO: Installed plugins: []  
[2025-06-02 15:22:07] local.INFO: Installed plugins: []  
[2025-06-02 15:22:42] local.INFO: Installed plugins: []  
[2025-06-02 15:22:43] local.INFO: Installed plugins: []  
[2025-06-02 15:22:45] local.INFO: Installed plugins: []  
[2025-06-02 15:22:49] local.INFO: Installed plugins: []  
[2025-06-02 15:22:52] local.INFO: Installed plugins: []  
[2025-06-02 15:22:53] local.INFO: Installed plugins: []  
[2025-06-02 15:23:07] local.INFO: Installed plugins: []  
[2025-06-02 15:23:07] local.INFO: Installed plugins: []  
[2025-06-02 15:23:29] local.INFO: Installed plugins: []  
[2025-06-02 15:23:47] local.INFO: Installed plugins: []  
[2025-06-02 15:24:07] local.INFO: Installed plugins: []  
[2025-06-02 15:24:07] local.INFO: Installed plugins: []  
[2025-06-02 15:24:11] local.INFO: Installed plugins: []  
[2025-06-02 15:24:28] local.INFO: Installed plugins: []  
[2025-06-02 15:24:37] local.INFO: Installed plugins: []  
[2025-06-02 15:24:41] local.INFO: Installed plugins: []  
[2025-06-02 15:24:51] local.INFO: Installed plugins: []  
[2025-06-02 15:24:53] local.INFO: Installed plugins: []  
[2025-06-02 15:24:57] local.INFO: Installed plugins: []  
[2025-06-02 15:24:58] local.INFO: Installed plugins: []  
[2025-06-02 15:25:01] local.INFO: Installed plugins: []  
[2025-06-02 15:25:03] local.INFO: Installed plugins: []  
[2025-06-02 15:25:07] local.INFO: Installed plugins: []  
[2025-06-02 15:25:07] local.INFO: Installed plugins: []  
[2025-06-02 15:25:07] local.INFO: Installed plugins: []  
[2025-06-02 15:25:32] local.INFO: Installed plugins: []  
[2025-06-02 15:25:32] local.INFO: Installed plugins: []  
[2025-06-02 15:25:33] local.INFO: Installed plugins: []  
[2025-06-02 15:25:34] local.INFO: Installed plugins: []  
[2025-06-02 15:25:35] local.INFO: Installed plugins: []  
[2025-06-02 15:25:41] local.INFO: Installed plugins: []  
[2025-06-02 15:25:43] local.INFO: Installed plugins: []  
[2025-06-02 15:25:44] local.INFO: Installed plugins: []  
[2025-06-02 15:25:45] local.INFO: Installed plugins: []  
[2025-06-02 15:25:45] local.INFO: Installed plugins: []  
[2025-06-02 15:25:45] local.INFO: Installed plugins: []  
[2025-06-02 15:25:46] local.INFO: Installed plugins: []  
[2025-06-02 15:25:51] local.INFO: Installed plugins: []  
[2025-06-02 15:25:56] local.INFO: Installed plugins: []  
[2025-06-02 15:25:56] local.INFO: Installed plugins: []  
[2025-06-02 15:26:06] local.INFO: Installed plugins: []  
[2025-06-02 15:26:07] local.INFO: Installed plugins: []  
[2025-06-02 15:26:07] local.INFO: Installed plugins: []  
[2025-06-02 15:26:07] local.INFO: Installed plugins: []  
[2025-06-02 15:26:11] local.INFO: Installed plugins: []  
[2025-06-02 15:26:16] local.INFO: Installed plugins: []  
[2025-06-02 15:26:21] local.INFO: Installed plugins: []  
[2025-06-02 15:26:23] local.INFO: Installed plugins: []  
[2025-06-02 15:26:24] local.INFO: Installed plugins: []  
[2025-06-02 15:26:29] local.INFO: Installed plugins: []  
[2025-06-02 15:26:35] local.INFO: Installed plugins: []  
[2025-06-02 15:26:36] local.INFO: Installed plugins: []  
[2025-06-02 15:26:37] local.INFO: Installed plugins: []  
[2025-06-02 15:26:37] local.INFO: Installed plugins: []  
[2025-06-02 15:26:38] local.INFO: Installed plugins: []  
[2025-06-02 15:26:40] local.INFO: Installed plugins: []  
[2025-06-02 15:26:43] local.INFO: Installed plugins: []  
[2025-06-02 15:26:43] local.INFO: Installed plugins: []  
[2025-06-02 15:26:44] local.INFO: Installed plugins: []  
[2025-06-02 15:26:45] local.INFO: Installed plugins: []  
[2025-06-02 15:26:50] local.INFO: Installed plugins: []  
[2025-06-02 15:26:50] local.INFO: Installed plugins: []  
[2025-06-02 15:26:52] local.INFO: Installed plugins: []  
[2025-06-02 15:27:05] local.INFO: Installed plugins: []  
[2025-06-02 15:27:06] local.INFO: Installed plugins: []  
[2025-06-02 15:27:07] local.INFO: Installed plugins: []  
[2025-06-02 15:27:07] local.INFO: Installed plugins: []  
[2025-06-02 15:27:07] local.INFO: Installed plugins: []  
[2025-06-02 15:27:07] local.INFO: Installed plugins: []  
[2025-06-02 15:27:08] local.INFO: Installed plugins: []  
[2025-06-02 15:27:10] local.INFO: Installed plugins: []  
[2025-06-02 15:27:13] local.INFO: Installed plugins: []  
[2025-06-02 15:27:14] local.INFO: Installed plugins: []  
[2025-06-02 15:27:15] local.INFO: Installed plugins: []  
[2025-06-02 15:27:15] local.INFO: Installed plugins: []  
[2025-06-02 15:27:15] local.INFO: Installed plugins: []  
[2025-06-02 15:27:15] local.INFO: Installed plugins: []  
[2025-06-02 15:27:16] local.INFO: Installed plugins: []  
[2025-06-02 15:27:17] local.INFO: Installed plugins: []  
[2025-06-02 15:27:22] local.INFO: Installed plugins: []  
[2025-06-02 15:27:26] local.INFO: Installed plugins: []  
[2025-06-02 15:27:27] local.INFO: Installed plugins: []  
[2025-06-02 15:28:07] local.INFO: Installed plugins: []  
[2025-06-02 15:28:58] local.INFO: Installed plugins: []  
[2025-06-02 15:28:59] local.INFO: Installed plugins: []  
[2025-06-02 15:28:59] local.INFO: Installed plugins: []  
[2025-06-02 15:28:59] local.INFO: Installed plugins: []  
[2025-06-02 15:28:59] local.INFO: Installed plugins: []  
[2025-06-02 15:29:06] local.INFO: Installed plugins: []  
[2025-06-02 15:29:07] local.INFO: Installed plugins: []  
[2025-06-02 15:29:07] local.INFO: Installed plugins: []  
[2025-06-02 15:29:43] local.INFO: Installed plugins: []  
[2025-06-02 15:30:07] local.INFO: Installed plugins: []  
[2025-06-02 15:30:07] local.INFO: Installed plugins: []  
[2025-06-02 15:31:01] local.INFO: Installed plugins: []  
[2025-06-02 15:31:07] local.INFO: Installed plugins: []  
[2025-06-02 15:31:07] local.INFO: Installed plugins: []  
[2025-06-02 15:31:33] local.INFO: Installed plugins: []  
[2025-06-02 15:32:07] local.INFO: Installed plugins: []  
[2025-06-02 15:32:13] local.INFO: Installed plugins: []  
[2025-06-02 15:32:53] local.INFO: Installed plugins: []  
[2025-06-02 15:33:00] local.INFO: Installed plugins: []  
[2025-06-02 15:33:06] local.INFO: Installed plugins: []  
[2025-06-02 15:33:07] local.INFO: Installed plugins: []  
[2025-06-02 15:33:07] local.INFO: Installed plugins: []  
[2025-06-02 15:33:07] local.INFO: Installed plugins: []  
[2025-06-02 15:33:13] local.INFO: Installed plugins: []  
[2025-06-02 15:33:13] local.INFO: Installed plugins: []  
[2025-06-02 15:33:20] local.INFO: Installed plugins: []  
[2025-06-02 15:33:28] local.INFO: Installed plugins: []  
[2025-06-02 15:33:32] local.INFO: Installed plugins: []  
[2025-06-02 15:33:35] local.INFO: Installed plugins: []  
[2025-06-02 15:33:38] local.INFO: Installed plugins: []  
[2025-06-02 15:33:41] local.INFO: Installed plugins: []  
[2025-06-02 15:33:42] local.INFO: Installed plugins: []  
[2025-06-02 15:33:47] local.INFO: Installed plugins: []  
[2025-06-02 15:33:53] local.INFO: Installed plugins: []  
[2025-06-02 15:33:55] local.INFO: Installed plugins: []  
[2025-06-02 15:34:07] local.INFO: Installed plugins: []  
[2025-06-02 15:34:07] local.INFO: Installed plugins: []  
[2025-06-02 15:34:07] local.INFO: Installed plugins: []  
[2025-06-02 15:34:10] local.INFO: Installed plugins: []  
[2025-06-02 15:34:15] local.INFO: Installed plugins: []  
[2025-06-02 15:35:07] local.INFO: Installed plugins: []  
[2025-06-02 15:35:07] local.INFO: Installed plugins: []  
[2025-06-02 15:35:07] local.INFO: Installed plugins: []  
[2025-06-02 15:35:33] local.INFO: Installed plugins: []  
[2025-06-02 15:35:52] local.INFO: Installed plugins: []  
[2025-06-02 15:35:58] local.INFO: Installed plugins: []  
[2025-06-02 15:36:07] local.INFO: Installed plugins: []  
[2025-06-02 15:36:07] local.INFO: Installed plugins: []  
[2025-06-02 15:36:44] local.INFO: Installed plugins: []  
[2025-06-02 15:36:57] local.INFO: Installed plugins: []  
[2025-06-02 15:37:05] local.INFO: Installed plugins: []  
[2025-06-02 15:37:07] local.INFO: Installed plugins: []  
[2025-06-02 15:37:07] local.INFO: Installed plugins: []  
[2025-06-02 15:37:13] local.INFO: Installed plugins: []  
[2025-06-02 15:37:27] local.INFO: Installed plugins: []  
[2025-06-02 15:37:33] local.INFO: Installed plugins: []  
[2025-06-02 15:37:39] local.INFO: Installed plugins: []  
[2025-06-02 15:37:40] local.ERROR: The "--show" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--show\" option does not exist. at E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('show', NULL)
#1 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--show')
#2 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--show', true)
#3 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\TableCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 E:\\projects\\My\\sucos\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\projects\\My\\sucos\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-02 15:37:45] local.INFO: Installed plugins: []  
[2025-06-02 15:37:46] local.INFO: Installed plugins: []  
[2025-06-02 15:37:46] local.INFO: Installed plugins: []  
[2025-06-02 15:37:47] local.INFO: Installed plugins: []  
[2025-06-02 15:37:47] local.INFO: Installed plugins: []  
[2025-06-02 15:37:47] local.INFO: Installed plugins: []  
[2025-06-02 15:37:47] local.INFO: Installed plugins: []  
[2025-06-02 15:37:48] local.INFO: Installed plugins: []  
[2025-06-02 15:37:54] local.INFO: Installed plugins: []  
[2025-06-02 15:37:58] local.INFO: Installed plugins: []  
[2025-06-02 15:38:02] local.INFO: Installed plugins: []  
[2025-06-02 15:38:04] local.INFO: Installed plugins: []  
[2025-06-02 15:38:07] local.INFO: Installed plugins: []  
[2025-06-02 15:38:07] local.INFO: Installed plugins: []  
[2025-06-02 15:38:08] local.INFO: Installed plugins: []  
[2025-06-02 15:38:15] local.INFO: Installed plugins: []  
[2025-06-02 15:38:15] local.INFO: Installed plugins: []  
[2025-06-02 15:38:15] local.INFO: Installed plugins: []  
[2025-06-02 15:38:17] local.INFO: Installed plugins: []  
[2025-06-02 15:38:22] local.INFO: Installed plugins: []  
[2025-06-02 15:38:23] local.INFO: Installed plugins: []  
[2025-06-02 15:38:26] local.INFO: Installed plugins: []  
[2025-06-02 15:39:07] local.INFO: Installed plugins: []  
[2025-06-02 15:39:07] local.INFO: Installed plugins: []  
[2025-06-02 15:40:07] local.INFO: Installed plugins: []  
[2025-06-02 15:41:07] local.INFO: Installed plugins: []  
[2025-06-02 15:42:07] local.INFO: Installed plugins: []  
[2025-06-02 15:42:07] local.INFO: Installed plugins: []  
[2025-06-02 15:43:07] local.INFO: Installed plugins: []  
[2025-06-02 15:43:07] local.INFO: Installed plugins: []  
[2025-06-02 15:43:51] local.INFO: Installed plugins: []  
[2025-06-02 15:43:56] local.INFO: Installed plugins: []  
[2025-06-02 15:44:03] local.INFO: Installed plugins: []  
[2025-06-02 15:44:07] local.INFO: Installed plugins: []  
[2025-06-02 15:44:07] local.INFO: Installed plugins: []  
[2025-06-02 15:45:07] local.INFO: Installed plugins: []  
[2025-06-02 15:45:07] local.INFO: Installed plugins: []  
[2025-06-02 15:45:07] local.INFO: Installed plugins: []  
[2025-06-02 15:46:07] local.INFO: Installed plugins: []  
[2025-06-02 15:46:07] local.INFO: Installed plugins: []  
[2025-06-02 15:47:07] local.INFO: Installed plugins: []  
[2025-06-02 15:47:07] local.INFO: Installed plugins: []  
[2025-06-02 15:48:07] local.INFO: Installed plugins: []  
[2025-06-02 15:48:07] local.INFO: Installed plugins: []  
[2025-06-02 15:49:07] local.INFO: Installed plugins: []  
[2025-06-02 15:49:07] local.INFO: Installed plugins: []  
[2025-06-02 15:50:07] local.INFO: Installed plugins: []  
[2025-06-02 15:50:07] local.INFO: Installed plugins: []  
[2025-06-02 15:51:07] local.INFO: Installed plugins: []  
[2025-06-02 15:51:08] local.INFO: Installed plugins: []  
[2025-06-02 15:52:07] local.INFO: Installed plugins: []  
[2025-06-02 15:53:07] local.INFO: Installed plugins: []  
[2025-06-02 15:53:08] local.INFO: Installed plugins: []  
[2025-06-02 15:53:23] local.INFO: Installed plugins: []  
[2025-06-02 15:53:32] local.INFO: Installed plugins: []  
[2025-06-02 15:53:42] local.INFO: Installed plugins: []  
[2025-06-02 15:54:07] local.INFO: Installed plugins: []  
[2025-06-02 15:54:07] local.INFO: Installed plugins: []  
[2025-06-02 15:55:07] local.INFO: Installed plugins: []  
[2025-06-02 15:55:08] local.INFO: Installed plugins: []  
[2025-06-02 15:55:08] local.INFO: Installed plugins: []  
[2025-06-02 15:56:08] local.INFO: Installed plugins: []  
[2025-06-02 15:56:08] local.INFO: Installed plugins: []  
[2025-06-02 15:57:08] local.INFO: Installed plugins: []  
[2025-06-02 15:57:12] local.INFO: Installed plugins: []  
[2025-06-02 15:57:19] local.INFO: Installed plugins: []  
[2025-06-02 15:57:26] local.INFO: Installed plugins: []  
[2025-06-02 15:57:39] local.INFO: Installed plugins: []  
[2025-06-02 15:57:39] local.INFO: Installed plugins: []  
[2025-06-02 15:57:42] local.INFO: Installed plugins: []  
[2025-06-02 15:57:53] local.INFO: Installed plugins: []  
[2025-06-02 15:58:08] local.INFO: Installed plugins: []  
[2025-06-02 15:58:27] local.INFO: Installed plugins: []  
[2025-06-02 15:58:27] local.INFO: Installed plugins: []  
[2025-06-02 15:58:35] local.INFO: Installed plugins: []  
[2025-06-02 15:58:37] local.INFO: Installed plugins: []  
[2025-06-02 15:58:43] local.INFO: Installed plugins: []  
[2025-06-02 15:59:03] local.INFO: Installed plugins: []  
[2025-06-02 15:59:03] local.INFO: Installed plugins: []  
[2025-06-02 15:59:08] local.INFO: Installed plugins: []  
[2025-06-02 15:59:08] local.INFO: Installed plugins: []  
[2025-06-02 15:59:10] local.INFO: Installed plugins: []  
[2025-06-02 15:59:14] local.INFO: Installed plugins: []  
[2025-06-02 15:59:16] local.INFO: Installed plugins: []  
[2025-06-02 15:59:17] local.INFO: Installed plugins: []  
[2025-06-02 15:59:18] local.INFO: Installed plugins: []  
[2025-06-02 15:59:19] local.INFO: Installed plugins: []  
[2025-06-02 15:59:20] local.INFO: Installed plugins: []  
[2025-06-02 15:59:21] local.INFO: Installed plugins: []  
[2025-06-02 15:59:22] local.INFO: Installed plugins: []  
[2025-06-02 15:59:25] local.INFO: Installed plugins: []  
[2025-06-02 15:59:25] local.INFO: Installed plugins: []  
[2025-06-02 15:59:28] local.INFO: Installed plugins: []  
[2025-06-02 15:59:29] local.INFO: Installed plugins: []  
[2025-06-02 15:59:30] local.INFO: Installed plugins: []  
[2025-06-02 15:59:36] local.INFO: Installed plugins: []  
[2025-06-02 15:59:42] local.INFO: Installed plugins: []  
[2025-06-02 15:59:44] local.INFO: Installed plugins: []  
[2025-06-02 15:59:45] local.INFO: Installed plugins: []  
[2025-06-02 15:59:46] local.INFO: Installed plugins: []  
[2025-06-02 15:59:52] local.INFO: Installed plugins: []  
[2025-06-02 16:00:01] local.INFO: Installed plugins: []  
[2025-06-02 16:00:03] local.INFO: Installed plugins: []  
[2025-06-02 16:00:04] local.INFO: Installed plugins: []  
[2025-06-02 16:00:05] local.INFO: Installed plugins: []  
[2025-06-02 16:00:06] local.INFO: Installed plugins: []  
[2025-06-02 16:00:07] local.INFO: Installed plugins: []  
[2025-06-02 16:00:08] local.INFO: Installed plugins: []  
[2025-06-02 16:00:08] local.INFO: Installed plugins: []  
[2025-06-02 16:00:08] local.INFO: Installed plugins: []  
[2025-06-02 16:00:09] local.INFO: Installed plugins: []  
[2025-06-02 16:00:10] local.INFO: Installed plugins: []  
[2025-06-02 16:00:11] local.INFO: Installed plugins: []  
[2025-06-02 16:00:11] local.INFO: Installed plugins: []  
[2025-06-02 16:00:12] local.INFO: Installed plugins: []  
[2025-06-02 16:00:13] local.INFO: Installed plugins: []  
[2025-06-02 16:00:16] local.INFO: Installed plugins: []  
[2025-06-02 16:00:21] local.INFO: Installed plugins: []  
[2025-06-02 16:01:08] local.INFO: Installed plugins: []  
[2025-06-02 16:01:08] local.INFO: Installed plugins: []  
[2025-06-02 16:01:10] local.INFO: Installed plugins: []  
[2025-06-02 16:02:08] local.INFO: Installed plugins: []  
[2025-06-02 16:02:08] local.INFO: Installed plugins: []  
[2025-06-02 16:03:08] local.INFO: Installed plugins: []  
[2025-06-02 16:03:08] local.INFO: Installed plugins: []  
[2025-06-02 16:03:33] local.INFO: Installed plugins: []  
[2025-06-02 16:03:34] local.INFO: Installed plugins: []  
[2025-06-02 16:03:37] local.INFO: Installed plugins: []  
[2025-06-02 16:03:48] local.INFO: Installed plugins: []  
[2025-06-02 16:03:50] local.INFO: Installed plugins: []  
[2025-06-02 16:03:51] local.INFO: Installed plugins: []  
[2025-06-02 16:03:52] local.INFO: Installed plugins: []  
[2025-06-02 16:04:01] local.INFO: Installed plugins: []  
[2025-06-02 16:04:08] local.INFO: Installed plugins: []  
[2025-06-02 16:04:08] local.INFO: Installed plugins: []  
[2025-06-02 16:04:17] local.INFO: Installed plugins: []  
[2025-06-02 16:04:18] local.INFO: Installed plugins: []  
[2025-06-02 16:04:30] local.INFO: Installed plugins: []  
[2025-06-02 16:04:31] local.INFO: Installed plugins: []  
[2025-06-02 16:04:33] local.INFO: Installed plugins: []  
[2025-06-02 16:04:33] local.INFO: Installed plugins: []  
[2025-06-02 16:04:38] local.INFO: Installed plugins: []  
[2025-06-02 16:05:07] local.INFO: Installed plugins: []  
[2025-06-02 16:05:07] local.INFO: Installed plugins: []  
[2025-06-02 16:05:08] local.INFO: Installed plugins: []  
[2025-06-02 16:05:08] local.INFO: Installed plugins: []  
[2025-06-02 16:05:09] local.INFO: Installed plugins: []  
[2025-06-02 16:05:10] local.INFO: Installed plugins: []  
[2025-06-02 16:05:11] local.INFO: Installed plugins: []  
[2025-06-02 16:05:13] local.INFO: Installed plugins: []  
[2025-06-02 16:05:15] local.INFO: Installed plugins: []  
[2025-06-02 16:05:45] local.INFO: Installed plugins: []  
[2025-06-02 16:05:55] local.INFO: Installed plugins: []  
[2025-06-02 16:05:57] local.INFO: Installed plugins: []  
[2025-06-02 16:06:04] local.INFO: Installed plugins: []  
[2025-06-02 16:06:04] local.INFO: Installed plugins: []  
[2025-06-02 16:06:08] local.INFO: Installed plugins: []  
[2025-06-02 16:06:08] local.INFO: Installed plugins: []  
[2025-06-02 16:06:14] local.INFO: Installed plugins: []  
[2025-06-02 16:06:15] local.INFO: Installed plugins: []  
[2025-06-02 16:06:16] local.INFO: Installed plugins: []  
[2025-06-02 16:06:18] local.INFO: Installed plugins: []  
[2025-06-02 16:06:20] local.INFO: Installed plugins: []  
[2025-06-02 16:06:21] local.INFO: Installed plugins: []  
[2025-06-02 16:06:39] local.INFO: Installed plugins: []  
[2025-06-02 16:06:43] local.INFO: Installed plugins: []  
[2025-06-02 16:07:08] local.INFO: Installed plugins: []  
[2025-06-02 16:07:08] local.INFO: Installed plugins: []  
[2025-06-02 16:08:08] local.INFO: Installed plugins: []  
[2025-06-02 16:08:37] local.INFO: Installed plugins: []  
[2025-06-02 16:09:08] local.INFO: Installed plugins: []  
[2025-06-02 16:10:06] local.INFO: Installed plugins: []  
[2025-06-02 16:10:08] local.INFO: Installed plugins: []  
[2025-06-02 16:10:08] local.INFO: Installed plugins: []  
[2025-06-02 16:10:12] local.INFO: Installed plugins: []  
[2025-06-02 16:10:26] local.INFO: Installed plugins: []  
[2025-06-02 16:10:27] local.INFO: Installed plugins: []  
[2025-06-02 16:11:08] local.INFO: Installed plugins: []  
[2025-06-02 16:11:08] local.INFO: Installed plugins: []  
[2025-06-02 16:11:34] local.INFO: Installed plugins: []  
[2025-06-02 16:12:08] local.INFO: Installed plugins: []  
[2025-06-02 16:12:08] local.INFO: Installed plugins: []  
[2025-06-02 16:12:47] local.INFO: Installed plugins: []  
[2025-06-02 16:13:08] local.INFO: Installed plugins: []  
[2025-06-02 16:13:08] local.INFO: Installed plugins: []  
[2025-06-02 16:13:27] local.INFO: Installed plugins: []  
[2025-06-02 16:13:45] local.INFO: Installed plugins: []  
[2025-06-02 16:14:08] local.INFO: Installed plugins: []  
[2025-06-02 16:14:08] local.INFO: Installed plugins: []  
[2025-06-02 16:14:42] local.INFO: Installed plugins: []  
[2025-06-02 16:15:07] local.INFO: Installed plugins: []  
[2025-06-02 16:15:08] local.INFO: Installed plugins: []  
[2025-06-02 16:15:08] local.INFO: Installed plugins: []  
[2025-06-02 16:15:08] local.INFO: Installed plugins: []  
[2025-06-02 16:15:32] local.INFO: Installed plugins: []  
[2025-06-02 16:15:38] local.INFO: Installed plugins: []  
[2025-06-02 16:16:01] local.INFO: Installed plugins: []  
[2025-06-02 16:16:07] local.INFO: Installed plugins: []  
[2025-06-02 16:16:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist (Connection: mysql, SQL: insert into `exports` (`user_id`, `exporter`, `total_rows`, `file_disk`, `updated_at`, `created_at`) values (2, App\Filament\Exports\ExpenseExporter, 1, local, 2025-06-02 16:16:07, 2025-06-02 16:16:07)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist (Connection: mysql, SQL: insert into `exports` (`user_id`, `exporter`, `total_rows`, `file_disk`, `updated_at`, `created_at`) values (2, App\\Filament\\Exports\\ExpenseExporter, 1, local, 2025-06-02 16:16:07, 2025-06-02 16:16:07)) at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `ex...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ex...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ex...', Array, 'id')
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ex...', Array, 'id')
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\CanExportRecords.php(177): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Actions\\ExportAction->{closure:Filament\\Actions\\Concerns\\CanExportRecords::setUp():108}(Object(Filament\\Actions\\ExportAction), Array, Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#11 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#12 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#17 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#18 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#19 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#23 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#72 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare('insert into `ex...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `ex...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `ex...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ex...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ex...', Array, 'id')
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ex...', Array, 'id')
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\CanExportRecords.php(177): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Actions\\ExportAction->{closure:Filament\\Actions\\Concerns\\CanExportRecords::setUp():108}(Object(Filament\\Actions\\ExportAction), Array, Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports))
#12 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#13 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#19 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#74 {main}
"} 
[2025-06-02 16:16:08] local.INFO: Installed plugins: []  
[2025-06-02 16:16:08] local.INFO: Installed plugins: []  
[2025-06-02 16:16:08] local.INFO: Installed plugins: []  
[2025-06-02 16:16:09] local.INFO: Installed plugins: []  
[2025-06-02 16:16:11] local.INFO: Installed plugins: []  
[2025-06-02 16:16:20] local.INFO: Installed plugins: []  
[2025-06-02 16:17:08] local.INFO: Installed plugins: []  
[2025-06-02 16:17:08] local.INFO: Installed plugins: []  
[2025-06-02 16:17:43] local.INFO: Installed plugins: []  
[2025-06-02 16:17:43] local.INFO: Installed plugins: []  
[2025-06-02 16:17:44] local.INFO: Installed plugins: []  
[2025-06-02 16:17:51] local.INFO: Installed plugins: []  
[2025-06-02 16:17:57] local.INFO: Installed plugins: []  
[2025-06-02 16:18:01] local.INFO: Installed plugins: []  
[2025-06-02 16:18:08] local.INFO: Installed plugins: []  
[2025-06-02 16:18:08] local.INFO: Installed plugins: []  
[2025-06-02 16:18:59] local.INFO: Installed plugins: []  
[2025-06-02 16:19:00] local.INFO: Installed plugins: []  
[2025-06-02 16:19:00] local.INFO: Installed plugins: []  
[2025-06-02 16:19:00] local.INFO: Installed plugins: []  
[2025-06-02 16:19:00] local.INFO: Installed plugins: []  
[2025-06-02 16:19:01] local.INFO: Installed plugins: []  
[2025-06-02 16:19:03] local.INFO: Installed plugins: []  
[2025-06-02 16:19:03] local.INFO: Installed plugins: []  
[2025-06-02 16:19:03] local.INFO: Installed plugins: []  
[2025-06-02 16:19:03] local.INFO: Installed plugins: []  
[2025-06-02 16:19:06] local.INFO: Installed plugins: []  
[2025-06-02 16:19:08] local.INFO: Installed plugins: []  
[2025-06-02 16:19:08] local.INFO: Installed plugins: []  
[2025-06-02 16:19:11] local.INFO: Installed plugins: []  
[2025-06-02 16:19:17] local.INFO: Installed plugins: []  
[2025-06-02 16:19:21] local.INFO: Installed plugins: []  
[2025-06-02 16:19:28] local.INFO: Installed plugins: []  
[2025-06-02 16:19:33] local.INFO: Installed plugins: []  
[2025-06-02 16:19:38] local.INFO: Installed plugins: []  
[2025-06-02 16:19:53] local.INFO: Installed plugins: []  
[2025-06-02 16:20:03] local.INFO: Installed plugins: []  
[2025-06-02 16:20:08] local.INFO: Installed plugins: []  
[2025-06-02 16:20:08] local.INFO: Installed plugins: []  
[2025-06-02 16:20:13] local.INFO: Installed plugins: []  
[2025-06-02 16:20:18] local.INFO: Installed plugins: []  
[2025-06-02 16:20:32] local.INFO: Installed plugins: []  
[2025-06-02 16:20:36] local.INFO: Installed plugins: []  
[2025-06-02 16:20:41] local.INFO: Installed plugins: []  
[2025-06-02 16:20:46] local.INFO: Installed plugins: []  
[2025-06-02 16:20:51] local.INFO: Installed plugins: []  
[2025-06-02 16:20:56] local.INFO: Installed plugins: []  
[2025-06-02 16:21:01] local.INFO: Installed plugins: []  
[2025-06-02 16:21:06] local.INFO: Installed plugins: []  
[2025-06-02 16:21:08] local.INFO: Installed plugins: []  
[2025-06-02 16:21:08] local.INFO: Installed plugins: []  
[2025-06-02 16:21:13] local.INFO: Installed plugins: []  
[2025-06-02 16:21:18] local.INFO: Installed plugins: []  
[2025-06-02 16:21:38] local.INFO: Installed plugins: []  
[2025-06-02 16:21:43] local.INFO: Installed plugins: []  
[2025-06-02 16:21:53] local.INFO: Installed plugins: []  
[2025-06-02 16:22:03] local.INFO: Installed plugins: []  
[2025-06-02 16:22:08] local.INFO: Installed plugins: []  
[2025-06-02 16:22:08] local.INFO: Installed plugins: []  
[2025-06-02 16:22:25] local.INFO: Installed plugins: []  
[2025-06-02 16:22:33] local.INFO: Installed plugins: []  
[2025-06-02 16:22:43] local.INFO: Installed plugins: []  
[2025-06-02 16:22:58] local.INFO: Installed plugins: []  
[2025-06-02 16:23:08] local.INFO: Installed plugins: []  
[2025-06-02 16:23:13] local.INFO: Installed plugins: []  
[2025-06-02 16:23:18] local.INFO: Installed plugins: []  
[2025-06-02 16:23:23] local.INFO: Installed plugins: []  
[2025-06-02 16:24:08] local.INFO: Installed plugins: []  
[2025-06-02 16:24:08] local.INFO: Installed plugins: []  
[2025-06-02 16:24:58] local.INFO: Installed plugins: []  
[2025-06-02 16:24:59] local.INFO: Installed plugins: []  
[2025-06-02 16:25:07] local.INFO: Installed plugins: []  
[2025-06-02 16:25:08] local.INFO: Installed plugins: []  
[2025-06-02 16:25:08] local.INFO: Installed plugins: []  
[2025-06-02 16:26:08] local.INFO: Installed plugins: []  
[2025-06-02 16:26:08] local.INFO: Installed plugins: []  
[2025-06-02 16:27:08] local.INFO: Installed plugins: []  
[2025-06-02 16:27:08] local.INFO: Installed plugins: []  
[2025-06-02 16:27:58] local.INFO: Installed plugins: []  
[2025-06-02 16:28:08] local.INFO: Installed plugins: []  
[2025-06-02 16:28:08] local.INFO: Installed plugins: []  
[2025-06-02 16:29:08] local.INFO: Installed plugins: []  
[2025-06-02 16:30:08] local.INFO: Installed plugins: []  
[2025-06-02 16:31:08] local.INFO: Installed plugins: []  
[2025-06-02 16:31:08] local.INFO: Installed plugins: []  
[2025-06-02 16:32:08] local.INFO: Installed plugins: []  
[2025-06-02 16:33:08] local.INFO: Installed plugins: []  
[2025-06-02 16:33:08] local.INFO: Installed plugins: []  
[2025-06-02 16:34:02] local.INFO: Installed plugins: []  
[2025-06-02 16:34:06] local.INFO: Installed plugins: []  
[2025-06-02 16:34:06] local.INFO: Installed plugins: []  
[2025-06-02 16:34:08] local.INFO: Installed plugins: []  
[2025-06-02 16:34:11] local.INFO: Installed plugins: []  
[2025-06-02 16:34:14] local.INFO: Installed plugins: []  
[2025-06-02 16:34:14] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist (Connection: mysql, SQL: insert into `exports` (`user_id`, `exporter`, `total_rows`, `file_disk`, `updated_at`, `created_at`) values (2, App\Filament\Exports\ExpenseExporter, 2, local, 2025-06-02 16:34:14, 2025-06-02 16:34:14)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist (Connection: mysql, SQL: insert into `exports` (`user_id`, `exporter`, `total_rows`, `file_disk`, `updated_at`, `created_at`) values (2, App\\Filament\\Exports\\ExpenseExporter, 2, local, 2025-06-02 16:34:14, 2025-06-02 16:34:14)) at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `ex...', Array, Object(Closure))
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ex...', Array, Object(Closure))
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ex...', Array, 'id')
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ex...', Array, 'id')
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\CanExportRecords.php(177): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Actions\\ExportAction->{closure:Filament\\Actions\\Concerns\\CanExportRecords::setUp():108}(Object(Filament\\Actions\\ExportAction), Array, Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#11 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#12 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#13 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#17 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#18 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#19 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#22 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#23 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#72 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sucos.exports' doesn't exist at E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare('insert into `ex...')
#1 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `ex...', Array)
#2 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `ex...', Array, Object(Closure))
#3 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ex...', Array, Object(Closure))
#4 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ex...', Array, 'id')
#5 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ex...', Array, 'id')
#6 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\CanExportRecords.php(177): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\projects\\My\\sucos\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Actions\\ExportAction->{closure:Filament\\Actions\\Concerns\\CanExportRecords::setUp():108}(Object(Filament\\Actions\\ExportAction), Array, Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports))
#12 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#13 E:\\projects\\My\\sucos\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#14 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#15 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#16 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#19 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#20 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\ExpenseReportResource\\Pages\\ListExpenseReports), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#21 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#22 E:\\projects\\My\\sucos\\vendor\\livewire\\volt\\src\\LivewireManager.php(35): Livewire\\LivewireManager->update(Array, Array, Array)
#23 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\Volt\\LivewireManager->update(Array, Array, Array)
#24 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#25 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#26 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#27 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\sucos\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#30 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\sucos\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#45 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#47 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\sucos\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\projects\\My\\sucos\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#56 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#60 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#64 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#66 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#68 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#70 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 E:\\projects\\My\\sucos\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 E:\\projects\\My\\sucos\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#74 {main}
"} 
[2025-06-02 16:34:15] local.INFO: Installed plugins: []  
